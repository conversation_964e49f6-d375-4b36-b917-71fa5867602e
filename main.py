#!/usr/bin/env python3
"""
Main entry point for the Advanced Investment Telegram Bot.
"""
import asyncio
import signal
import sys
import os
from pathlib import Path
import structlog

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from bot import InvestmentBot
from database import init_database, cleanup_database

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class BotApplication:
    """Main application class for the Investment Bot."""
    
    def __init__(self):
        self.bot: InvestmentBot = None
        self.shutdown_event = asyncio.Event()
        self.running = False
    
    async def startup(self) -> None:
        """Initialize and start the bot application."""
        try:
            logger.info("Starting Advanced Investment Telegram Bot...")
            
            # Create logs directory
            os.makedirs("logs", exist_ok=True)
            
            # Initialize bot
            self.bot = InvestmentBot()
            await self.bot.initialize()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Start the bot
            await self.bot.start()
            self.running = True
            
            logger.info("Bot started successfully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error("Failed to start bot", error=str(e))
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the bot application."""
        if not self.running:
            return
        
        try:
            logger.info("Shutting down bot...")
            
            if self.bot:
                await self.bot.stop()
            
            # Cleanup database connections
            await cleanup_database()
            
            self.running = False
            logger.info("Bot shutdown completed")
            
        except Exception as e:
            logger.error("Error during shutdown", error=str(e))
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal", signal=signum)
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)


async def main():
    """Main function."""
    try:
        # Validate configuration
        config.validate()
        logger.info("Configuration validated successfully")
        
        # Create and run application
        app = BotApplication()
        await app.startup()
        
    except Exception as e:
        logger.error("Application failed to start", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
