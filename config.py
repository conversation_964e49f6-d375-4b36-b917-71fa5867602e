"""
Configuration management for the Advanced Investment Telegram Bot.
"""
import os
from typing import List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    url: str
    pool_size: int = 20
    max_overflow: int = 30
    echo: bool = False


@dataclass
class RedisConfig:
    """Redis configuration settings."""
    url: str
    password: Optional[str] = None
    db: int = 0


@dataclass
class TelegramConfig:
    """Telegram bot configuration settings."""
    token: str
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    max_connections: int = 40
    allowed_updates: List[str] = None


@dataclass
class SecurityConfig:
    """Security configuration settings."""
    secret_key: str
    jwt_secret_key: str
    encryption_key: str
    token_expire_hours: int = 24


@dataclass
class APIConfig:
    """External API configuration settings."""
    coinmarketcap_api_key: Optional[str] = None
    alpha_vantage_api_key: Optional[str] = None
    binance_api_key: Optional[str] = None
    binance_secret_key: Optional[str] = None


@dataclass
class InvestmentConfig:
    """Investment-related configuration settings."""
    min_investment_amount: float = 10.0
    max_investment_amount: float = 10000.0
    default_currency: str = "USD"
    supported_cryptocurrencies: List[str] = None
    commission_rate: float = 0.01  # 1% commission


@dataclass
class AppConfig:
    """Main application configuration."""
    environment: str = "development"
    debug: bool = False
    log_level: str = "INFO"
    log_file_path: str = "logs/bot.log"
    max_users_per_hour: int = 100
    rate_limit_requests: int = 30
    rate_limit_window: int = 60
    admin_user_ids: List[int] = None
    super_admin_id: Optional[int] = None
    maintenance_mode: bool = False
    maintenance_message: str = "Bot is under maintenance. Please try again later."


class Config:
    """Main configuration class that loads all settings."""
    
    def __init__(self):
        self.database = DatabaseConfig(
            url=os.getenv("DATABASE_URL", "sqlite:///investment_bot.db"),
            pool_size=int(os.getenv("DATABASE_POOL_SIZE", "20")),
            max_overflow=int(os.getenv("DATABASE_MAX_OVERFLOW", "30")),
            echo=os.getenv("DEBUG", "False").lower() == "true"
        )
        
        self.redis = RedisConfig(
            url=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
            password=os.getenv("REDIS_PASSWORD")
        )
        
        self.telegram = TelegramConfig(
            token=os.getenv("TELEGRAM_BOT_TOKEN", ""),
            webhook_url=os.getenv("TELEGRAM_WEBHOOK_URL"),
            webhook_secret=os.getenv("TELEGRAM_WEBHOOK_SECRET"),
            allowed_updates=["message", "callback_query", "inline_query"]
        )
        
        self.security = SecurityConfig(
            secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-this"),
            jwt_secret_key=os.getenv("JWT_SECRET_KEY", "your-jwt-secret-change-this"),
            encryption_key=os.getenv("ENCRYPTION_KEY", "your-encryption-key-change-this")
        )
        
        self.api = APIConfig(
            coinmarketcap_api_key=os.getenv("COINMARKETCAP_API_KEY"),
            alpha_vantage_api_key=os.getenv("ALPHA_VANTAGE_API_KEY"),
            binance_api_key=os.getenv("BINANCE_API_KEY"),
            binance_secret_key=os.getenv("BINANCE_SECRET_KEY")
        )
        
        # Parse supported cryptocurrencies
        crypto_str = os.getenv("SUPPORTED_CRYPTOCURRENCIES", "BTC,ETH,TRX,USDT,BNB")
        supported_cryptos = [crypto.strip() for crypto in crypto_str.split(",")]
        
        self.investment = InvestmentConfig(
            min_investment_amount=float(os.getenv("MIN_INVESTMENT_AMOUNT", "10.0")),
            max_investment_amount=float(os.getenv("MAX_INVESTMENT_AMOUNT", "10000.0")),
            default_currency=os.getenv("DEFAULT_CURRENCY", "USD"),
            supported_cryptocurrencies=supported_cryptos
        )
        
        # Parse admin user IDs
        admin_ids_str = os.getenv("ADMIN_USER_IDS", "")
        admin_ids = []
        if admin_ids_str:
            admin_ids = [int(uid.strip()) for uid in admin_ids_str.split(",") if uid.strip()]
        
        self.app = AppConfig(
            environment=os.getenv("ENVIRONMENT", "development"),
            debug=os.getenv("DEBUG", "False").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_file_path=os.getenv("LOG_FILE_PATH", "logs/bot.log"),
            max_users_per_hour=int(os.getenv("MAX_USERS_PER_HOUR", "100")),
            rate_limit_requests=int(os.getenv("RATE_LIMIT_REQUESTS", "30")),
            rate_limit_window=int(os.getenv("RATE_LIMIT_WINDOW", "60")),
            admin_user_ids=admin_ids,
            super_admin_id=int(os.getenv("SUPER_ADMIN_ID")) if os.getenv("SUPER_ADMIN_ID") else None,
            maintenance_mode=os.getenv("MAINTENANCE_MODE", "False").lower() == "true",
            maintenance_message=os.getenv("MAINTENANCE_MESSAGE", "Bot is under maintenance. Please try again later.")
        )
    
    def validate(self) -> bool:
        """Validate that all required configuration is present."""
        if not self.telegram.token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")
        
        if not self.security.secret_key or self.security.secret_key == "your-secret-key-change-this":
            raise ValueError("SECRET_KEY must be set to a secure value")
        
        if not self.security.jwt_secret_key or self.security.jwt_secret_key == "your-jwt-secret-change-this":
            raise ValueError("JWT_SECRET_KEY must be set to a secure value")
        
        return True


# Global configuration instance
config = Config()
