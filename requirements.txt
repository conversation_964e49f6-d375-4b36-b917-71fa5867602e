# Core Telegram Bot Dependencies
python-telegram-bot==20.7
python-dotenv==1.0.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Security and Authentication
cryptography==41.0.8
bcrypt==4.1.2
pyjwt==2.8.0

# Data Validation
pydantic==2.5.2
marshmallow==3.20.2

# HTTP Requests and APIs
aiohttp==3.9.1
requests==2.31.0

# Async Support
asyncio-mqtt==0.16.1
asyncpg==0.29.0

# Logging and Monitoring
structlog==23.2.0
sentry-sdk==1.39.2

# Data Processing
pandas==2.1.4
numpy==1.26.2

# Cryptocurrency/Financial APIs
ccxt==4.1.92
yfinance==0.2.28

# Caching
redis==5.0.1
aioredis==2.0.1

# Configuration
pyyaml==6.0.1
click==8.1.7

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0

# Development
black==23.12.0
flake8==6.1.0
mypy==1.8.0
pre-commit==3.6.0
