"""
Command handlers for the Advanced Investment Telegram Bot.
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, ContextTypes
import structlog

from config import config
from services.user_service import UserService
from services.investment_service import InvestmentService
from services.market_service import MarketService
from bot.utils import BotUtils

logger = structlog.get_logger(__name__)


class CommandHandlers:
    """Class containing all command handlers."""
    
    def __init__(self):
        self.user_service = UserService()
        self.investment_service = InvestmentService()
        self.market_service = MarketService()
        self.bot_utils = BotUtils()
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command."""
        try:
            user = update.effective_user
            chat = update.effective_chat
            
            # Create or update user
            db_user = await self.user_service.update_or_create_user(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                language_code=user.language_code
            )
            
            # Check for referral code
            referral_code = None
            if context.args and len(context.args) > 0:
                referral_code = context.args[0]
                await self.user_service.process_referral(user.id, referral_code)
            
            # Create welcome message
            welcome_text = f"""
🎉 <b>Welcome to Advanced Investment Bot!</b>

Hello {user.first_name}! I'm your personal investment assistant, here to help you:

💰 <b>Track your investments</b>
📊 <b>Monitor market prices</b>
📈 <b>Analyze portfolio performance</b>
🔔 <b>Set price alerts</b>
💡 <b>Get investment insights</b>

<i>Let's start building your financial future together!</i>

Use the menu below to explore all features:
            """
            
            # Create inline keyboard
            keyboard = [
                [
                    InlineKeyboardButton("📊 My Portfolio", callback_data="portfolio_main"),
                    InlineKeyboardButton("💹 Markets", callback_data="markets_main")
                ],
                [
                    InlineKeyboardButton("🔔 Alerts", callback_data="alerts_main"),
                    InlineKeyboardButton("⚙️ Settings", callback_data="settings_main")
                ],
                [
                    InlineKeyboardButton("📚 Help", callback_data="help_main"),
                    InlineKeyboardButton("ℹ️ About", callback_data="about_main")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                welcome_text,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
            
            logger.info("Start command processed", user_id=user.id, referral_code=referral_code)
            
        except Exception as e:
            logger.error("Error in start command", user_id=user.id, error=str(e))
            await update.message.reply_text(
                "❌ An error occurred. Please try again later.",
                parse_mode="HTML"
            )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /help command."""
        try:
            help_text = """
📚 <b>Bot Commands & Features</b>

<b>🔧 Basic Commands:</b>
/start - Start the bot and see main menu
/help - Show this help message
/portfolio - View your investment portfolio
/markets - Check market prices
/alerts - Manage price alerts
/settings - Configure your preferences

<b>💰 Investment Features:</b>
• Track multiple portfolios
• Real-time price updates
• Profit/loss calculations
• Performance analytics
• Investment history

<b>📊 Market Features:</b>
• Live cryptocurrency prices
• Stock market data
• Market trends and analysis
• Price charts and indicators

<b>🔔 Alert Features:</b>
• Price alerts for any asset
• Portfolio value alerts
• Market movement notifications
• Custom alert conditions

<b>⚙️ Settings:</b>
• Preferred currency
• Notification preferences
• Privacy settings
• Risk tolerance

<b>🆘 Need Help?</b>
Contact support: @YourSupportBot
            """
            
            keyboard = [
                [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                help_text,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error("Error in help command", error=str(e))
            await update.message.reply_text("❌ An error occurred. Please try again later.")
    
    async def portfolio_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /portfolio command."""
        try:
            user_id = update.effective_user.id
            
            # Get user's portfolios
            portfolios = await self.investment_service.get_user_portfolios(user_id)
            
            if not portfolios:
                text = """
📊 <b>Your Portfolio</b>

You don't have any portfolios yet. Let's create your first one!

A portfolio helps you organize and track your investments. You can have multiple portfolios for different strategies.
                """
                
                keyboard = [
                    [InlineKeyboardButton("➕ Create Portfolio", callback_data="portfolio_create")],
                    [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
                ]
            else:
                # Show portfolio summary
                total_value = sum(p.total_value for p in portfolios)
                total_invested = sum(p.total_invested for p in portfolios)
                total_pnl = total_value - total_invested
                pnl_percentage = (total_pnl / total_invested * 100) if total_invested > 0 else 0
                
                pnl_emoji = "📈" if total_pnl >= 0 else "📉"
                pnl_color = "🟢" if total_pnl >= 0 else "🔴"
                
                text = f"""
📊 <b>Your Portfolio Summary</b>

💰 <b>Total Value:</b> ${total_value:,.2f}
💵 <b>Total Invested:</b> ${total_invested:,.2f}
{pnl_emoji} <b>P&L:</b> {pnl_color} ${total_pnl:,.2f} ({pnl_percentage:+.2f}%)

<b>📁 Your Portfolios ({len(portfolios)}):</b>
                """
                
                for portfolio in portfolios[:5]:  # Show first 5 portfolios
                    p_pnl = portfolio.total_value - portfolio.total_invested
                    p_pnl_pct = (p_pnl / portfolio.total_invested * 100) if portfolio.total_invested > 0 else 0
                    p_emoji = "📈" if p_pnl >= 0 else "📉"
                    
                    text += f"\n{p_emoji} <b>{portfolio.name}</b>: ${portfolio.total_value:,.2f} ({p_pnl_pct:+.2f}%)"
                
                keyboard = [
                    [
                        InlineKeyboardButton("📊 View Details", callback_data="portfolio_details"),
                        InlineKeyboardButton("➕ Add Investment", callback_data="investment_add")
                    ],
                    [
                        InlineKeyboardButton("📈 Analytics", callback_data="portfolio_analytics"),
                        InlineKeyboardButton("⚙️ Manage", callback_data="portfolio_manage")
                    ],
                    [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
                ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                text,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error("Error in portfolio command", error=str(e))
            await update.message.reply_text("❌ An error occurred. Please try again later.")
    
    async def markets_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /markets command."""
        try:
            # Get top cryptocurrencies
            top_cryptos = await self.market_service.get_top_cryptocurrencies(limit=10)
            
            text = "💹 <b>Market Overview</b>\n\n"
            
            if top_cryptos:
                text += "<b>🔥 Top Cryptocurrencies:</b>\n"
                for i, crypto in enumerate(top_cryptos[:5], 1):
                    price_change = crypto.get('price_change_24h', 0)
                    change_emoji = "📈" if price_change >= 0 else "📉"
                    change_color = "🟢" if price_change >= 0 else "🔴"
                    
                    text += f"{i}. <b>{crypto['symbol']}</b> - ${crypto['price']:,.2f} "
                    text += f"{change_emoji} {change_color} {price_change:+.2f}%\n"
            else:
                text += "❌ Unable to fetch market data at the moment."
            
            keyboard = [
                [
                    InlineKeyboardButton("🔍 Search Asset", callback_data="market_search"),
                    InlineKeyboardButton("📊 Top Gainers", callback_data="market_gainers")
                ],
                [
                    InlineKeyboardButton("📉 Top Losers", callback_data="market_losers"),
                    InlineKeyboardButton("💰 Watchlist", callback_data="market_watchlist")
                ],
                [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                text,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error("Error in markets command", error=str(e))
            await update.message.reply_text("❌ An error occurred. Please try again later.")
    
    async def price_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /price command with symbol argument."""
        try:
            if not context.args:
                await update.message.reply_text(
                    "💡 Usage: /price <symbol>\n\nExample: /price BTC or /price AAPL",
                    parse_mode="HTML"
                )
                return
            
            symbol = context.args[0].upper()
            
            # Get price data
            price_data = await self.market_service.get_asset_price(symbol)
            
            if not price_data:
                await update.message.reply_text(
                    f"❌ Could not find price data for {symbol}. Please check the symbol and try again.",
                    parse_mode="HTML"
                )
                return
            
            price_change = price_data.get('price_change_24h', 0)
            change_emoji = "📈" if price_change >= 0 else "📉"
            change_color = "🟢" if price_change >= 0 else "🔴"
            
            text = f"""
💰 <b>{price_data['name']} ({symbol})</b>

💵 <b>Price:</b> ${price_data['price']:,.2f}
{change_emoji} <b>24h Change:</b> {change_color} {price_change:+.2f}%
📊 <b>Volume:</b> ${price_data.get('volume_24h', 0):,.0f}
📈 <b>Market Cap:</b> ${price_data.get('market_cap', 0):,.0f}

<i>Last updated: {price_data.get('last_updated', 'Unknown')}</i>
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("📊 Chart", callback_data=f"chart_{symbol}"),
                    InlineKeyboardButton("🔔 Set Alert", callback_data=f"alert_set_{symbol}")
                ],
                [
                    InlineKeyboardButton("➕ Add to Portfolio", callback_data=f"portfolio_add_{symbol}"),
                    InlineKeyboardButton("⭐ Add to Watchlist", callback_data=f"watchlist_add_{symbol}")
                ],
                [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                text,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error("Error in price command", error=str(e))
            await update.message.reply_text("❌ An error occurred. Please try again later.")


async def setup_command_handlers(application: Application) -> None:
    """Setup all command handlers."""
    try:
        handlers = CommandHandlers()
        
        # Add command handlers
        application.add_handler(CommandHandler("start", handlers.start_command))
        application.add_handler(CommandHandler("help", handlers.help_command))
        application.add_handler(CommandHandler("portfolio", handlers.portfolio_command))
        application.add_handler(CommandHandler("markets", handlers.markets_command))
        application.add_handler(CommandHandler("price", handlers.price_command))
        
        logger.info("Command handlers setup completed")
        
    except Exception as e:
        logger.error("Failed to setup command handlers", error=str(e))
        raise
