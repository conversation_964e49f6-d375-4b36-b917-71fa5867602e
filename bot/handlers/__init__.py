"""
Bot handlers package for the Advanced Investment Telegram Bot.
"""
from .command_handlers import setup_command_handlers
from .message_handlers import setup_message_handlers
from .callback_handlers import setup_callback_handlers
from .inline_handlers import setup_inline_handlers
from .admin_handlers import setup_admin_handlers

from telegram.ext import Application
import structlog

logger = structlog.get_logger(__name__)


async def setup_handlers(application: Application) -> None:
    """Setup all bot handlers."""
    try:
        logger.info("Setting up bot handlers...")
        
        # Setup different types of handlers
        await setup_command_handlers(application)
        await setup_message_handlers(application)
        await setup_callback_handlers(application)
        await setup_inline_handlers(application)
        await setup_admin_handlers(application)
        
        logger.info("All bot handlers setup completed")
        
    except Exception as e:
        logger.error("Failed to setup bot handlers", error=str(e))
        raise


__all__ = [
    "setup_handlers",
    "setup_command_handlers",
    "setup_message_handlers", 
    "setup_callback_handlers",
    "setup_inline_handlers",
    "setup_admin_handlers"
]
