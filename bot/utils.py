"""
Utility functions and helpers for the bot.
"""
import re
import asyncio
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
from decimal import Decimal
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import ContextTypes
import structlog

logger = structlog.get_logger(__name__)


class BotUtils:
    """Utility class with helper functions for the bot."""
    
    @staticmethod
    def format_currency(amount: Union[float, Decimal], currency: str = "USD", decimals: int = 2) -> str:
        """Format currency amount with proper symbols and formatting."""
        try:
            if isinstance(amount, Decimal):
                amount = float(amount)
            
            currency_symbols = {
                "USD": "$",
                "EUR": "€",
                "GBP": "£",
                "JPY": "¥",
                "BTC": "₿",
                "ETH": "Ξ"
            }
            
            symbol = currency_symbols.get(currency.upper(), currency.upper())
            
            if amount >= 1_000_000:
                return f"{symbol}{amount/1_000_000:.1f}M"
            elif amount >= 1_000:
                return f"{symbol}{amount/1_000:.1f}K"
            else:
                return f"{symbol}{amount:,.{decimals}f}"
                
        except Exception as e:
            logger.error("Error formatting currency", amount=amount, currency=currency, error=str(e))
            return f"{currency} {amount}"
    
    @staticmethod
    def format_percentage(percentage: Union[float, Decimal], decimals: int = 2) -> str:
        """Format percentage with proper sign and color indicators."""
        try:
            if isinstance(percentage, Decimal):
                percentage = float(percentage)
            
            sign = "+" if percentage > 0 else ""
            color_indicator = "🟢" if percentage >= 0 else "🔴"
            
            return f"{color_indicator} {sign}{percentage:.{decimals}f}%"
            
        except Exception as e:
            logger.error("Error formatting percentage", percentage=percentage, error=str(e))
            return f"{percentage}%"
    
    @staticmethod
    def format_large_number(number: Union[int, float, Decimal]) -> str:
        """Format large numbers with K, M, B suffixes."""
        try:
            if isinstance(number, Decimal):
                number = float(number)
            
            if abs(number) >= 1_000_000_000:
                return f"{number/1_000_000_000:.1f}B"
            elif abs(number) >= 1_000_000:
                return f"{number/1_000_000:.1f}M"
            elif abs(number) >= 1_000:
                return f"{number/1_000:.1f}K"
            else:
                return f"{number:,.0f}"
                
        except Exception as e:
            logger.error("Error formatting large number", number=number, error=str(e))
            return str(number)
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """Validate if symbol format is correct."""
        if not symbol:
            return False
        
        # Basic validation: 1-10 characters, alphanumeric
        pattern = r'^[A-Z0-9]{1,10}$'
        return bool(re.match(pattern, symbol.upper()))
    
    @staticmethod
    def validate_amount(amount_str: str) -> Optional[Decimal]:
        """Validate and convert amount string to Decimal."""
        try:
            # Remove common formatting characters
            cleaned = re.sub(r'[,$\s]', '', amount_str)
            
            # Validate format
            if not re.match(r'^\d+(\.\d+)?$', cleaned):
                return None
            
            amount = Decimal(cleaned)
            
            # Check reasonable bounds
            if amount <= 0 or amount > Decimal('1000000000'):
                return None
            
            return amount
            
        except Exception:
            return None
    
    @staticmethod
    def create_pagination_keyboard(
        current_page: int,
        total_pages: int,
        callback_prefix: str,
        additional_buttons: Optional[List[List[InlineKeyboardButton]]] = None
    ) -> InlineKeyboardMarkup:
        """Create pagination keyboard for long lists."""
        keyboard = []
        
        # Add additional buttons first
        if additional_buttons:
            keyboard.extend(additional_buttons)
        
        # Pagination row
        pagination_row = []
        
        # Previous button
        if current_page > 1:
            pagination_row.append(
                InlineKeyboardButton("⬅️ Prev", callback_data=f"{callback_prefix}_page_{current_page-1}")
            )
        
        # Page indicator
        pagination_row.append(
            InlineKeyboardButton(f"{current_page}/{total_pages}", callback_data="noop")
        )
        
        # Next button
        if current_page < total_pages:
            pagination_row.append(
                InlineKeyboardButton("Next ➡️", callback_data=f"{callback_prefix}_page_{current_page+1}")
            )
        
        if pagination_row:
            keyboard.append(pagination_row)
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def create_confirmation_keyboard(
        confirm_callback: str,
        cancel_callback: str = "cancel",
        confirm_text: str = "✅ Confirm",
        cancel_text: str = "❌ Cancel"
    ) -> InlineKeyboardMarkup:
        """Create confirmation keyboard."""
        keyboard = [
            [
                InlineKeyboardButton(confirm_text, callback_data=confirm_callback),
                InlineKeyboardButton(cancel_text, callback_data=cancel_callback)
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def escape_markdown(text: str) -> str:
        """Escape markdown special characters."""
        escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        
        for char in escape_chars:
            text = text.replace(char, f'\\{char}')
        
        return text
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 4000, suffix: str = "...") -> str:
        """Truncate text to fit Telegram message limits."""
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def parse_time_period(period_str: str) -> Optional[timedelta]:
        """Parse time period string (e.g., '1h', '30m', '7d') to timedelta."""
        try:
            pattern = r'^(\d+)([smhd])$'
            match = re.match(pattern, period_str.lower())
            
            if not match:
                return None
            
            value, unit = match.groups()
            value = int(value)
            
            if unit == 's':
                return timedelta(seconds=value)
            elif unit == 'm':
                return timedelta(minutes=value)
            elif unit == 'h':
                return timedelta(hours=value)
            elif unit == 'd':
                return timedelta(days=value)
            
            return None
            
        except Exception:
            return None
    
    @staticmethod
    def format_time_ago(dt: datetime) -> str:
        """Format datetime as 'time ago' string."""
        try:
            now = datetime.utcnow()
            diff = now - dt
            
            if diff.days > 0:
                return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
            elif diff.seconds >= 3600:
                hours = diff.seconds // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif diff.seconds >= 60:
                minutes = diff.seconds // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            else:
                return "Just now"
                
        except Exception:
            return "Unknown"
    
    @staticmethod
    def generate_chart_url(
        symbol: str,
        period: str = "1d",
        chart_type: str = "line"
    ) -> str:
        """Generate chart URL for price visualization."""
        # This is a placeholder - in a real implementation, you'd integrate with
        # a charting service like TradingView, CoinGecko, or create your own charts
        base_url = "https://api.example.com/chart"
        return f"{base_url}?symbol={symbol}&period={period}&type={chart_type}"
    
    @staticmethod
    async def send_typing_action(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send typing action to show bot is processing."""
        try:
            if update.effective_chat:
                await context.bot.send_chat_action(
                    chat_id=update.effective_chat.id,
                    action="typing"
                )
        except Exception:
            pass  # Ignore errors for typing action
    
    @staticmethod
    async def safe_delete_message(
        context: ContextTypes.DEFAULT_TYPE,
        chat_id: int,
        message_id: int
    ) -> bool:
        """Safely delete a message without raising exceptions."""
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
            return True
        except Exception:
            return False
    
    @staticmethod
    def extract_command_args(text: str) -> List[str]:
        """Extract command arguments from message text."""
        if not text:
            return []
        
        # Remove bot command and split by whitespace
        parts = text.split()[1:]  # Skip the command itself
        return [arg.strip() for arg in parts if arg.strip()]
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """Validate email address format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """Validate phone number format."""
        # Remove common formatting characters
        cleaned = re.sub(r'[^\d+]', '', phone)
        
        # Basic validation: starts with + and has 7-15 digits
        pattern = r'^\+\d{7,15}$'
        return bool(re.match(pattern, cleaned))
    
    @staticmethod
    def create_progress_bar(current: int, total: int, length: int = 10) -> str:
        """Create a text-based progress bar."""
        if total == 0:
            return "█" * length
        
        filled = int(length * current / total)
        bar = "█" * filled + "░" * (length - filled)
        percentage = (current / total) * 100
        
        return f"{bar} {percentage:.1f}%"
    
    @staticmethod
    async def rate_limit_check(
        user_id: int,
        action: str,
        max_requests: int = 10,
        window_minutes: int = 1
    ) -> bool:
        """Simple in-memory rate limiting check."""
        # This is a basic implementation - in production, use Redis
        # For now, we'll just return True (no rate limiting)
        return True
    
    @staticmethod
    def format_error_message(error: str, user_friendly: bool = True) -> str:
        """Format error message for user display."""
        if user_friendly:
            # Map technical errors to user-friendly messages
            error_mappings = {
                "connection": "Connection error. Please try again later.",
                "timeout": "Request timed out. Please try again.",
                "invalid": "Invalid input. Please check your data and try again.",
                "not_found": "The requested item was not found.",
                "permission": "You don't have permission to perform this action.",
                "rate_limit": "Too many requests. Please wait a moment and try again."
            }
            
            error_lower = error.lower()
            for key, message in error_mappings.items():
                if key in error_lower:
                    return message
            
            return "An error occurred. Please try again later."
        
        return error
