"""
Main bot class for the Advanced Investment Telegram Bot.
"""
import asyncio
from typing import Optional, Dict, Any
from telegram import Update, Bot
from telegram.ext import (
    Application, 
    ApplicationBuilder, 
    ContextTypes,
    CommandHandler,
    MessageHandler,
    CallbackQueryHandler,
    InlineQueryHandler,
    filters
)
from telegram.error import TelegramError
import structlog

from config import config
from database import db_manager, init_database
from .handlers import setup_handlers
from .middleware import setup_middleware
from .utils import BotUtils
from services.user_service import UserService
from services.logging_service import LoggingService

logger = structlog.get_logger(__name__)


class InvestmentBot:
    """Main bot class that orchestrates all bot functionality."""
    
    def __init__(self):
        self.application: Optional[Application] = None
        self.bot: Optional[Bot] = None
        self.user_service: Optional[UserService] = None
        self.logging_service: Optional[LoggingService] = None
        self._running = False
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> None:
        """Initialize the bot and all its components."""
        try:
            logger.info("Initializing Investment Bot...")
            
            # Validate configuration
            config.validate()
            
            # Initialize database
            await init_database()
            await db_manager.create_tables()
            
            # Initialize services
            self.user_service = UserService()
            self.logging_service = LoggingService()
            
            # Create bot application
            self.application = (
                ApplicationBuilder()
                .token(config.telegram.token)
                .concurrent_updates(True)
                .build()
            )
            
            self.bot = self.application.bot
            
            # Setup middleware
            await setup_middleware(self.application)
            
            # Setup handlers
            await setup_handlers(self.application)
            
            # Setup error handler
            self.application.add_error_handler(self._error_handler)
            
            logger.info("Investment Bot initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize bot", error=str(e))
            raise
    
    async def start(self) -> None:
        """Start the bot."""
        if not self.application:
            raise RuntimeError("Bot not initialized")
        
        try:
            logger.info("Starting Investment Bot...")
            
            # Initialize the application
            await self.application.initialize()
            
            # Start the bot
            if config.telegram.webhook_url:
                # Webhook mode
                await self._start_webhook()
            else:
                # Polling mode
                await self._start_polling()
            
            self._running = True
            logger.info("Investment Bot started successfully")
            
        except Exception as e:
            logger.error("Failed to start bot", error=str(e))
            raise
    
    async def stop(self) -> None:
        """Stop the bot gracefully."""
        if not self.application or not self._running:
            return
        
        try:
            logger.info("Stopping Investment Bot...")
            
            # Stop the application
            await self.application.stop()
            await self.application.shutdown()
            
            # Close database connections
            await db_manager.close()
            
            self._running = False
            self._shutdown_event.set()
            
            logger.info("Investment Bot stopped successfully")
            
        except Exception as e:
            logger.error("Error stopping bot", error=str(e))
    
    async def _start_polling(self) -> None:
        """Start bot in polling mode."""
        logger.info("Starting bot in polling mode")
        
        # Start polling
        await self.application.start()
        await self.application.updater.start_polling(
            allowed_updates=config.telegram.allowed_updates,
            drop_pending_updates=True
        )
        
        # Wait for shutdown signal
        await self._shutdown_event.wait()
        
        # Stop polling
        await self.application.updater.stop()
    
    async def _start_webhook(self) -> None:
        """Start bot in webhook mode."""
        logger.info("Starting bot in webhook mode", webhook_url=config.telegram.webhook_url)
        
        # Set webhook
        await self.application.bot.set_webhook(
            url=config.telegram.webhook_url,
            secret_token=config.telegram.webhook_secret,
            allowed_updates=config.telegram.allowed_updates,
            max_connections=config.telegram.max_connections
        )
        
        # Start the application
        await self.application.start()
        
        # Wait for shutdown signal
        await self._shutdown_event.wait()
    
    async def _error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors that occur during update processing."""
        try:
            # Log the error
            error_msg = f"Exception while handling an update: {context.error}"
            logger.error(error_msg, update=str(update), error=str(context.error))
            
            # Log to database if possible
            if self.logging_service:
                await self.logging_service.log_error(
                    error_msg,
                    exception_info=(type(context.error), context.error, context.error.__traceback__),
                    extra_data={
                        "update": str(update),
                        "chat_id": getattr(update, "effective_chat", {}).get("id") if update else None,
                        "user_id": getattr(update, "effective_user", {}).get("id") if update else None,
                    }
                )
            
            # Send error message to user if possible
            if isinstance(update, Update) and update.effective_chat:
                try:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="❌ An error occurred while processing your request. Please try again later.",
                        parse_mode="HTML"
                    )
                except TelegramError:
                    # If we can't send the error message, just log it
                    logger.error("Failed to send error message to user")
        
        except Exception as e:
            logger.error("Error in error handler", error=str(e))
    
    async def send_message_to_user(
        self, 
        user_id: int, 
        text: str, 
        parse_mode: str = "HTML",
        **kwargs
    ) -> bool:
        """Send a message to a specific user."""
        if not self.bot:
            return False
        
        try:
            await self.bot.send_message(
                chat_id=user_id,
                text=text,
                parse_mode=parse_mode,
                **kwargs
            )
            return True
        except TelegramError as e:
            logger.error("Failed to send message to user", user_id=user_id, error=str(e))
            return False
    
    async def broadcast_message(
        self, 
        user_ids: list, 
        text: str, 
        parse_mode: str = "HTML",
        **kwargs
    ) -> Dict[str, int]:
        """Broadcast a message to multiple users."""
        if not self.bot:
            return {"sent": 0, "failed": 0}
        
        sent = 0
        failed = 0
        
        for user_id in user_ids:
            try:
                await self.bot.send_message(
                    chat_id=user_id,
                    text=text,
                    parse_mode=parse_mode,
                    **kwargs
                )
                sent += 1
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except TelegramError as e:
                logger.warning("Failed to send broadcast message", user_id=user_id, error=str(e))
                failed += 1
        
        logger.info("Broadcast completed", sent=sent, failed=failed, total=len(user_ids))
        return {"sent": sent, "failed": failed}
    
    async def get_bot_info(self) -> Dict[str, Any]:
        """Get bot information and statistics."""
        if not self.bot:
            return {}
        
        try:
            bot_info = await self.bot.get_me()
            
            # Get database statistics
            from database import DatabaseUtils
            db_stats = await DatabaseUtils.get_table_counts()
            
            return {
                "bot_info": {
                    "id": bot_info.id,
                    "username": bot_info.username,
                    "first_name": bot_info.first_name,
                    "can_join_groups": bot_info.can_join_groups,
                    "can_read_all_group_messages": bot_info.can_read_all_group_messages,
                    "supports_inline_queries": bot_info.supports_inline_queries,
                },
                "database_stats": db_stats,
                "status": {
                    "running": self._running,
                    "webhook_mode": bool(config.telegram.webhook_url),
                    "maintenance_mode": config.app.maintenance_mode,
                }
            }
        except Exception as e:
            logger.error("Failed to get bot info", error=str(e))
            return {"error": str(e)}
    
    @property
    def is_running(self) -> bool:
        """Check if bot is running."""
        return self._running
    
    def signal_shutdown(self) -> None:
        """Signal the bot to shutdown."""
        self._shutdown_event.set()
