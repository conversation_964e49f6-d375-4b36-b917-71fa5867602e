"""
Middleware for the Advanced Investment Telegram Bot.
"""
import time
import asyncio
from typing import Dict, Set, Optional
from datetime import datetime, timedelta
from telegram import Update
from telegram.ext import Application, ContextTypes, BaseHandler
import structlog

from config import config
from services.user_service import UserService
from services.logging_service import LoggingService

logger = structlog.get_logger(__name__)


class RateLimitMiddleware:
    """Rate limiting middleware to prevent spam and abuse."""
    
    def __init__(self, max_requests: int = 30, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.user_requests: Dict[int, list] = {}
        self.cleanup_interval = 300  # Clean up every 5 minutes
        self.last_cleanup = time.time()
    
    def _cleanup_old_requests(self) -> None:
        """Clean up old request records."""
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = current_time - self.window_seconds
        for user_id in list(self.user_requests.keys()):
            self.user_requests[user_id] = [
                req_time for req_time in self.user_requests[user_id]
                if req_time > cutoff_time
            ]
            if not self.user_requests[user_id]:
                del self.user_requests[user_id]
        
        self.last_cleanup = current_time
    
    def is_rate_limited(self, user_id: int) -> bool:
        """Check if user is rate limited."""
        current_time = time.time()
        cutoff_time = current_time - self.window_seconds
        
        # Clean up old requests periodically
        self._cleanup_old_requests()
        
        # Get user's recent requests
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        
        # Remove old requests
        self.user_requests[user_id] = [
            req_time for req_time in self.user_requests[user_id]
            if req_time > cutoff_time
        ]
        
        # Check if user has exceeded rate limit
        if len(self.user_requests[user_id]) >= self.max_requests:
            return True
        
        # Add current request
        self.user_requests[user_id].append(current_time)
        return False


class MaintenanceMiddleware:
    """Middleware to handle maintenance mode."""
    
    def __init__(self):
        self.maintenance_mode = config.app.maintenance_mode
        self.maintenance_message = config.app.maintenance_message
        self.admin_user_ids = set(config.app.admin_user_ids or [])
    
    def is_maintenance_mode(self) -> bool:
        """Check if bot is in maintenance mode."""
        return self.maintenance_mode
    
    def can_bypass_maintenance(self, user_id: int) -> bool:
        """Check if user can bypass maintenance mode."""
        return user_id in self.admin_user_ids


class UserTrackingMiddleware:
    """Middleware to track user activity and update user information."""
    
    def __init__(self):
        self.user_service: Optional[UserService] = None
        self.logging_service: Optional[LoggingService] = None
    
    def set_services(self, user_service: UserService, logging_service: LoggingService):
        """Set required services."""
        self.user_service = user_service
        self.logging_service = logging_service
    
    async def track_user_activity(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Track user activity and update user information."""
        if not update.effective_user or not self.user_service:
            return
        
        try:
            user = update.effective_user
            
            # Update or create user
            await self.user_service.update_or_create_user(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                language_code=user.language_code
            )
            
            # Log user interaction
            if self.logging_service and update.message:
                await self.logging_service.log_user_interaction(
                    user_id=user.id,
                    interaction_type="message",
                    action=update.message.text[:100] if update.message.text else "non_text_message",
                    success=True
                )
        
        except Exception as e:
            logger.error("Failed to track user activity", user_id=user.id, error=str(e))


class SecurityMiddleware:
    """Security middleware for additional protection."""
    
    def __init__(self):
        self.blocked_users: Set[int] = set()
        self.suspicious_patterns = [
            r'<script',
            r'javascript:',
            r'data:text/html',
            r'vbscript:',
        ]
    
    def is_user_blocked(self, user_id: int) -> bool:
        """Check if user is blocked."""
        return user_id in self.blocked_users
    
    def block_user(self, user_id: int) -> None:
        """Block a user."""
        self.blocked_users.add(user_id)
        logger.warning("User blocked", user_id=user_id)
    
    def unblock_user(self, user_id: int) -> None:
        """Unblock a user."""
        self.blocked_users.discard(user_id)
        logger.info("User unblocked", user_id=user_id)
    
    def is_suspicious_content(self, text: str) -> bool:
        """Check if content contains suspicious patterns."""
        if not text:
            return False
        
        import re
        text_lower = text.lower()
        
        for pattern in self.suspicious_patterns:
            if re.search(pattern, text_lower):
                return True
        
        return False


# Global middleware instances
rate_limiter = RateLimitMiddleware(
    max_requests=config.app.rate_limit_requests,
    window_seconds=config.app.rate_limit_window
)
maintenance_middleware = MaintenanceMiddleware()
user_tracking_middleware = UserTrackingMiddleware()
security_middleware = SecurityMiddleware()


async def middleware_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Main middleware handler that processes all middleware."""
    if not update.effective_user:
        return
    
    user_id = update.effective_user.id
    
    try:
        # Security check - blocked users
        if security_middleware.is_user_blocked(user_id):
            logger.warning("Blocked user attempted to use bot", user_id=user_id)
            return
        
        # Maintenance mode check
        if maintenance_middleware.is_maintenance_mode():
            if not maintenance_middleware.can_bypass_maintenance(user_id):
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=maintenance_middleware.maintenance_message
                )
                return
        
        # Rate limiting check
        if rate_limiter.is_rate_limited(user_id):
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="⚠️ You're sending messages too quickly. Please wait a moment and try again."
            )
            logger.warning("User rate limited", user_id=user_id)
            return
        
        # Security check - suspicious content
        if update.message and update.message.text:
            if security_middleware.is_suspicious_content(update.message.text):
                logger.warning("Suspicious content detected", user_id=user_id, content=update.message.text[:100])
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="⚠️ Your message contains suspicious content and has been blocked."
                )
                return
        
        # Track user activity
        await user_tracking_middleware.track_user_activity(update, context)
        
        # Add request start time for performance tracking
        context.user_data['request_start_time'] = time.time()
        
    except Exception as e:
        logger.error("Error in middleware", user_id=user_id, error=str(e))


async def setup_middleware(application: Application) -> None:
    """Setup all middleware for the bot application."""
    try:
        # Add middleware as a pre-processor
        # Note: python-telegram-bot doesn't have built-in middleware support,
        # so we'll add it as a handler with high priority
        from telegram.ext import MessageHandler, filters
        
        middleware_message_handler = MessageHandler(
            filters.ALL, 
            middleware_handler
        )
        
        # Add with highest priority (group -1)
        application.add_handler(middleware_message_handler, group=-1)
        
        logger.info("Middleware setup completed")
        
    except Exception as e:
        logger.error("Failed to setup middleware", error=str(e))
        raise


def set_middleware_services(user_service: UserService, logging_service: LoggingService) -> None:
    """Set services for middleware that need them."""
    user_tracking_middleware.set_services(user_service, logging_service)
