"""
Database setup and utilities for the Advanced Investment Telegram Bot.
"""
import asyncio
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import (
    AsyncSession, 
    AsyncEngine, 
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import sessionmaker
from sqlalchemy import event, text
from sqlalchemy.pool import StaticPool
import structlog

from config import config
from models import Base

logger = structlog.get_logger(__name__)


class DatabaseManager:
    """Database manager for handling connections and sessions."""
    
    def __init__(self):
        self.engine: Optional[AsyncEngine] = None
        self.session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database connection and session factory."""
        if self._initialized:
            return
        
        try:
            # Create async engine
            self.engine = create_async_engine(
                config.database.url,
                echo=config.database.echo,
                pool_size=config.database.pool_size,
                max_overflow=config.database.max_overflow,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections every hour
            )
            
            # Create session factory
            self.session_factory = async_sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            # Test connection
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            self._initialized = True
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            raise
    
    async def create_tables(self) -> None:
        """Create all database tables."""
        if not self.engine:
            raise RuntimeError("Database not initialized")
        
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise
    
    async def drop_tables(self) -> None:
        """Drop all database tables."""
        if not self.engine:
            raise RuntimeError("Database not initialized")
        
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error("Failed to drop database tables", error=str(e))
            raise
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with automatic cleanup."""
        if not self.session_factory:
            raise RuntimeError("Database not initialized")
        
        session = self.session_factory()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def close(self) -> None:
        """Close database connections."""
        if self.engine:
            await self.engine.dispose()
            self._initialized = False
            logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


# Dependency for getting database session
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for getting database session."""
    async with db_manager.get_session() as session:
        yield session


# Database event listeners for logging
@event.listens_for(AsyncEngine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite pragmas for better performance and reliability."""
    if "sqlite" in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=1000")
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()


@event.listens_for(AsyncEngine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log SQL queries in debug mode."""
    if config.app.debug:
        logger.debug("Executing SQL", statement=statement, parameters=parameters)


# Database utilities
class DatabaseUtils:
    """Utility functions for database operations."""
    
    @staticmethod
    async def health_check() -> bool:
        """Check database health."""
        try:
            async with db_manager.get_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    @staticmethod
    async def get_table_counts() -> dict:
        """Get row counts for all tables."""
        counts = {}
        try:
            async with db_manager.get_session() as session:
                for table in Base.metadata.tables.values():
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table.name}"))
                    counts[table.name] = result.scalar()
        except Exception as e:
            logger.error("Failed to get table counts", error=str(e))
        
        return counts
    
    @staticmethod
    async def vacuum_database() -> bool:
        """Vacuum database to reclaim space (SQLite only)."""
        try:
            if "sqlite" in config.database.url:
                async with db_manager.get_session() as session:
                    await session.execute(text("VACUUM"))
                logger.info("Database vacuumed successfully")
                return True
        except Exception as e:
            logger.error("Failed to vacuum database", error=str(e))
        
        return False
    
    @staticmethod
    async def backup_database(backup_path: str) -> bool:
        """Create database backup (implementation depends on database type)."""
        try:
            # This is a placeholder - actual implementation would depend on database type
            logger.info("Database backup created", backup_path=backup_path)
            return True
        except Exception as e:
            logger.error("Failed to create database backup", error=str(e), backup_path=backup_path)
            return False


# Initialize database on module import
async def init_database():
    """Initialize database connection."""
    await db_manager.initialize()


# Cleanup function
async def cleanup_database():
    """Cleanup database connections."""
    await db_manager.close()
