#!/usr/bin/env python3
"""
Setup script for the Advanced Investment Telegram Bot.
"""
import os
import sys
import asyncio
import secrets
import string
from pathlib import Path
import subprocess

def generate_secret_key(length: int = 64) -> str:
    """Generate a secure secret key."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")

def check_dependencies():
    """Check if required system dependencies are available."""
    dependencies = {
        'postgresql': ['psql', '--version'],
        'redis': ['redis-cli', '--version'],
        'git': ['git', '--version']
    }
    
    missing = []
    for name, cmd in dependencies.items():
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {name} is available")
            else:
                missing.append(name)
        except FileNotFoundError:
            missing.append(name)
    
    if missing:
        print(f"⚠️  Missing dependencies: {', '.join(missing)}")
        print("Please install them before continuing:")
        for dep in missing:
            if dep == 'postgresql':
                print("  - PostgreSQL: https://www.postgresql.org/download/")
            elif dep == 'redis':
                print("  - Redis: https://redis.io/download")
            elif dep == 'git':
                print("  - Git: https://git-scm.com/downloads")
    else:
        print("✅ All system dependencies are available")

def create_env_file():
    """Create .env file from template."""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if env_file.exists():
        response = input("📝 .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Skipping .env file creation")
            return
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return
    
    print("📝 Creating .env file...")
    
    # Read template
    with open(env_example, 'r') as f:
        content = f.read()
    
    # Generate secure keys
    secret_key = generate_secret_key(64)
    jwt_secret = generate_secret_key(64)
    encryption_key = generate_secret_key(32)
    
    # Replace placeholder values
    replacements = {
        'your_secret_key_here': secret_key,
        'your_jwt_secret_key': jwt_secret,
        'your_encryption_key': encryption_key,
    }
    
    for placeholder, value in replacements.items():
        content = content.replace(placeholder, value)
    
    # Write .env file
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ .env file created with secure keys")
    print("⚠️  Please edit .env file and add your:")
    print("   - Telegram bot token")
    print("   - Database credentials")
    print("   - API keys")

def create_directories():
    """Create necessary directories."""
    directories = ['logs', 'data', 'backups', 'temp']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_python_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
        
        # Install requirements
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        
        print("✅ Python dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

async def setup_database():
    """Setup database tables."""
    print("🗄️  Setting up database...")
    
    try:
        # Import after dependencies are installed
        from database import init_database, db_manager
        
        # Initialize database
        await init_database()
        
        # Create tables
        await db_manager.create_tables()
        
        print("✅ Database setup completed")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        print("Please check your database configuration in .env file")

def create_systemd_service():
    """Create systemd service file for Linux."""
    if os.name != 'posix':
        return
    
    response = input("🔧 Create systemd service file? (y/N): ")
    if response.lower() != 'y':
        return
    
    service_content = f"""[Unit]
Description=Advanced Investment Telegram Bot
After=network.target postgresql.service redis.service

[Service]
Type=simple
User={os.getenv('USER', 'bot')}
WorkingDirectory={os.getcwd()}
Environment=PATH={os.environ.get('PATH')}
ExecStart={sys.executable} main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path('investment-bot.service')
    with open(service_file, 'w') as f:
        f.write(service_content)
    
    print(f"✅ Systemd service file created: {service_file}")
    print("To install the service:")
    print(f"  sudo cp {service_file} /etc/systemd/system/")
    print("  sudo systemctl daemon-reload")
    print("  sudo systemctl enable investment-bot")
    print("  sudo systemctl start investment-bot")

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETED!")
    print("="*60)
    print("\n📋 Next steps:")
    print("1. Edit .env file with your configuration:")
    print("   - Add your Telegram bot token from @BotFather")
    print("   - Configure database connection")
    print("   - Add API keys for market data")
    print("\n2. Start PostgreSQL and Redis services")
    print("\n3. Run the bot:")
    print("   python main.py")
    print("\n4. Test the bot by sending /start to your Telegram bot")
    print("\n📚 Documentation:")
    print("   - README.md for detailed instructions")
    print("   - Check logs/ directory for application logs")
    print("\n🆘 Need help?")
    print("   - Check the GitHub repository for issues")
    print("   - Review the configuration in .env file")
    print("   - Ensure all services are running")

async def main():
    """Main setup function."""
    print("🚀 Advanced Investment Telegram Bot Setup")
    print("="*50)
    
    # Check system requirements
    check_python_version()
    check_dependencies()
    
    # Create necessary files and directories
    create_env_file()
    create_directories()
    
    # Install dependencies
    install_python_dependencies()
    
    # Setup database
    try:
        await setup_database()
    except ImportError:
        print("⚠️  Skipping database setup (dependencies not installed)")
    except Exception as e:
        print(f"⚠️  Database setup failed: {e}")
        print("You can run database setup later with:")
        print("python -c \"import asyncio; from database import init_database, db_manager; asyncio.run(init_database()); asyncio.run(db_manager.create_tables())\"")
    
    # Optional systemd service
    create_systemd_service()
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
