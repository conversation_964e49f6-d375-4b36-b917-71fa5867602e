"""
User service for managing user accounts, authentication, and profiles.
"""
import secrets
import string
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import select, update, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from database import get_db_session
from models.user import User, UserProfile, UserSession, UserRole, UserStatus
from services.security_service import SecurityService

logger = structlog.get_logger(__name__)


class UserService:
    """Service for managing users, authentication, and profiles."""
    
    def __init__(self):
        self.security_service = SecurityService()
    
    async def get_user_by_telegram_id(self, telegram_id: int) -> Optional[User]:
        """Get user by Telegram ID."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(User).where(
                        and_(
                            User.telegram_id == telegram_id,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by telegram ID", telegram_id=telegram_id, error=str(e))
            return None
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by internal ID."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(User).where(
                        and_(
                            User.id == user_id,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by ID", user_id=user_id, error=str(e))
            return None
    
    async def update_or_create_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: str = "",
        last_name: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> Optional[User]:
        """Update existing user or create new one."""
        try:
            async with get_db_session() as session:
                # Try to find existing user
                result = await session.execute(
                    select(User).where(User.telegram_id == telegram_id)
                )
                user = result.scalar_one_or_none()
                
                if user:
                    # Update existing user
                    user.username = username
                    user.first_name = first_name
                    user.last_name = last_name
                    user.language_code = language_code or "en"
                    user.update_last_activity()
                    
                    # Restore if soft deleted
                    if user.is_deleted:
                        user.restore()
                    
                    logger.info("User updated", user_id=user.id, telegram_id=telegram_id)
                else:
                    # Create new user
                    user = User(
                        telegram_id=telegram_id,
                        username=username,
                        first_name=first_name,
                        last_name=last_name,
                        language_code=language_code or "en",
                        referral_code=self._generate_referral_code()
                    )
                    session.add(user)
                    await session.flush()  # Get the ID
                    
                    # Create user profile
                    profile = UserProfile(user_id=user.id)
                    session.add(profile)
                    
                    logger.info("New user created", user_id=user.id, telegram_id=telegram_id)
                
                await session.commit()
                return user
                
        except Exception as e:
            logger.error("Failed to update or create user", telegram_id=telegram_id, error=str(e))
            return None
    
    async def get_user_profile(self, user_id: int) -> Optional[UserProfile]:
        """Get user profile."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(UserProfile).where(UserProfile.user_id == user_id)
                )
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user profile", user_id=user_id, error=str(e))
            return None
    
    async def update_user_profile(
        self,
        user_id: int,
        **profile_data
    ) -> Optional[UserProfile]:
        """Update user profile."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(UserProfile).where(UserProfile.user_id == user_id)
                )
                profile = result.scalar_one_or_none()
                
                if not profile:
                    # Create profile if it doesn't exist
                    profile = UserProfile(user_id=user_id)
                    session.add(profile)
                
                # Update profile fields
                for field, value in profile_data.items():
                    if hasattr(profile, field):
                        setattr(profile, field, value)
                
                await session.commit()
                logger.info("User profile updated", user_id=user_id)
                return profile
                
        except Exception as e:
            logger.error("Failed to update user profile", user_id=user_id, error=str(e))
            return None
    
    async def create_user_session(
        self,
        user_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_hours: int = 24
    ) -> Optional[UserSession]:
        """Create a new user session."""
        try:
            session_token = self.security_service.generate_session_token()
            expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
            
            async with get_db_session() as session:
                user_session = UserSession(
                    user_id=user_id,
                    session_token=session_token,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    expires_at=expires_at
                )
                session.add(user_session)
                await session.commit()
                
                logger.info("User session created", user_id=user_id, session_id=user_session.id)
                return user_session
                
        except Exception as e:
            logger.error("Failed to create user session", user_id=user_id, error=str(e))
            return None
    
    async def validate_user_session(self, session_token: str) -> Optional[UserSession]:
        """Validate user session token."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(UserSession).where(
                        and_(
                            UserSession.session_token == session_token,
                            UserSession.is_active == True,
                            UserSession.expires_at > datetime.utcnow()
                        )
                    )
                )
                user_session = result.scalar_one_or_none()
                
                if user_session:
                    # Update last used timestamp
                    user_session.last_used = datetime.utcnow()
                    await session.commit()
                
                return user_session
                
        except Exception as e:
            logger.error("Failed to validate user session", error=str(e))
            return None
    
    async def invalidate_user_session(self, session_token: str) -> bool:
        """Invalidate a user session."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    update(UserSession)
                    .where(UserSession.session_token == session_token)
                    .values(is_active=False)
                )
                await session.commit()
                
                return result.rowcount > 0
                
        except Exception as e:
            logger.error("Failed to invalidate user session", error=str(e))
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    update(UserSession)
                    .where(
                        or_(
                            UserSession.expires_at < datetime.utcnow(),
                            UserSession.is_active == False
                        )
                    )
                    .values(is_active=False)
                )
                await session.commit()
                
                logger.info("Expired sessions cleaned up", count=result.rowcount)
                return result.rowcount
                
        except Exception as e:
            logger.error("Failed to cleanup expired sessions", error=str(e))
            return 0
    
    async def update_user_role(self, user_id: int, role: UserRole) -> bool:
        """Update user role."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    update(User)
                    .where(User.id == user_id)
                    .values(role=role)
                )
                await session.commit()
                
                if result.rowcount > 0:
                    logger.info("User role updated", user_id=user_id, role=role.value)
                    return True
                return False
                
        except Exception as e:
            logger.error("Failed to update user role", user_id=user_id, error=str(e))
            return False
    
    async def update_user_status(self, user_id: int, status: UserStatus) -> bool:
        """Update user status."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    update(User)
                    .where(User.id == user_id)
                    .values(status=status)
                )
                await session.commit()
                
                if result.rowcount > 0:
                    logger.info("User status updated", user_id=user_id, status=status.value)
                    return True
                return False
                
        except Exception as e:
            logger.error("Failed to update user status", user_id=user_id, error=str(e))
            return False
    
    async def process_referral(self, user_id: int, referral_code: str) -> bool:
        """Process referral code for new user."""
        try:
            async with get_db_session() as session:
                # Find referring user
                result = await session.execute(
                    select(User).where(User.referral_code == referral_code)
                )
                referring_user = result.scalar_one_or_none()
                
                if not referring_user:
                    logger.warning("Invalid referral code", referral_code=referral_code)
                    return False
                
                # Update referred user
                result = await session.execute(
                    update(User)
                    .where(User.id == user_id)
                    .values(referred_by_id=referring_user.id)
                )
                
                if result.rowcount > 0:
                    await session.commit()
                    logger.info("Referral processed", user_id=user_id, referring_user_id=referring_user.id)
                    return True
                
                return False
                
        except Exception as e:
            logger.error("Failed to process referral", user_id=user_id, referral_code=referral_code, error=str(e))
            return False
    
    async def get_user_referrals(self, user_id: int) -> List[User]:
        """Get users referred by this user."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(User).where(
                        and_(
                            User.referred_by_id == user_id,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                return result.scalars().all()
        except Exception as e:
            logger.error("Failed to get user referrals", user_id=user_id, error=str(e))
            return []
    
    async def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get user statistics."""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return {}
            
            referrals = await self.get_user_referrals(user_id)
            
            return {
                "user_id": user.id,
                "telegram_id": user.telegram_id,
                "username": user.username,
                "full_name": user.full_name,
                "registration_date": user.registration_date.isoformat(),
                "last_activity": user.last_activity.isoformat() if user.last_activity else None,
                "role": user.role.value,
                "status": user.status.value,
                "is_verified": user.is_verified,
                "is_premium": user.is_premium,
                "referral_code": user.referral_code,
                "referrals_count": len(referrals),
            }
            
        except Exception as e:
            logger.error("Failed to get user stats", user_id=user_id, error=str(e))
            return {}
    
    def _generate_referral_code(self, length: int = 8) -> str:
        """Generate a unique referral code."""
        characters = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))
