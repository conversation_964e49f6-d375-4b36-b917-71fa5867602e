"""
Investment service for portfolio and investment management.
"""
from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from sqlalchemy import select, update, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from database import get_db_session
from models.investment import Portfolio, Investment, Transaction, InvestmentType, TransactionType, TransactionStatus
from services.market_service import MarketService

logger = structlog.get_logger(__name__)


class InvestmentService:
    """Service for managing investments and portfolios."""
    
    def __init__(self):
        self.market_service = MarketService()
    
    async def create_portfolio(
        self,
        user_id: int,
        name: str,
        description: Optional[str] = None,
        currency: str = "USD"
    ) -> Optional[Portfolio]:
        """Create a new portfolio for user."""
        try:
            async with get_db_session() as session:
                portfolio = Portfolio(
                    user_id=user_id,
                    name=name,
                    description=description,
                    currency=currency
                )
                
                session.add(portfolio)
                await session.commit()
                
                logger.info("Portfolio created", user_id=user_id, portfolio_id=portfolio.id, name=name)
                return portfolio
                
        except Exception as e:
            logger.error("Failed to create portfolio", user_id=user_id, error=str(e))
            return None
    
    async def get_user_portfolios(self, user_id: int) -> List[Portfolio]:
        """Get all portfolios for a user."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(Portfolio).where(
                        and_(
                            Portfolio.user_id == user_id,
                            Portfolio.deleted_at.is_(None)
                        )
                    ).order_by(Portfolio.created_at.desc())
                )
                
                portfolios = result.scalars().all()
                
                # Update portfolio values with current market prices
                for portfolio in portfolios:
                    await self._update_portfolio_value(portfolio)
                
                return portfolios
                
        except Exception as e:
            logger.error("Failed to get user portfolios", user_id=user_id, error=str(e))
            return []
    
    async def get_portfolio_by_id(self, portfolio_id: int, user_id: int) -> Optional[Portfolio]:
        """Get portfolio by ID (with user ownership check)."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(Portfolio).where(
                        and_(
                            Portfolio.id == portfolio_id,
                            Portfolio.user_id == user_id,
                            Portfolio.deleted_at.is_(None)
                        )
                    )
                )
                
                portfolio = result.scalar_one_or_none()
                
                if portfolio:
                    await self._update_portfolio_value(portfolio)
                
                return portfolio
                
        except Exception as e:
            logger.error("Failed to get portfolio", portfolio_id=portfolio_id, user_id=user_id, error=str(e))
            return None
    
    async def add_investment(
        self,
        portfolio_id: int,
        user_id: int,
        symbol: str,
        investment_type: InvestmentType,
        quantity: Decimal,
        purchase_price: Decimal,
        purchase_date: Optional[datetime] = None
    ) -> Optional[Investment]:
        """Add investment to portfolio."""
        try:
            # Verify portfolio ownership
            portfolio = await self.get_portfolio_by_id(portfolio_id, user_id)
            if not portfolio:
                logger.warning("Portfolio not found or access denied", portfolio_id=portfolio_id, user_id=user_id)
                return None
            
            async with get_db_session() as session:
                # Check if investment already exists
                result = await session.execute(
                    select(Investment).where(
                        and_(
                            Investment.portfolio_id == portfolio_id,
                            Investment.symbol == symbol.upper(),
                            Investment.deleted_at.is_(None)
                        )
                    )
                )
                existing_investment = result.scalar_one_or_none()
                
                if existing_investment:
                    # Update existing investment (average cost)
                    total_value = (existing_investment.quantity * existing_investment.average_cost) + (quantity * purchase_price)
                    total_quantity = existing_investment.quantity + quantity
                    new_average_cost = total_value / total_quantity
                    
                    existing_investment.quantity = total_quantity
                    existing_investment.average_cost = new_average_cost
                    existing_investment.total_invested += quantity * purchase_price
                    
                    investment = existing_investment
                else:
                    # Create new investment
                    investment = Investment(
                        portfolio_id=portfolio_id,
                        symbol=symbol.upper(),
                        investment_type=investment_type,
                        quantity=quantity,
                        average_cost=purchase_price,
                        current_price=purchase_price,
                        total_invested=quantity * purchase_price,
                        purchase_date=purchase_date or datetime.utcnow()
                    )
                    session.add(investment)
                
                # Create transaction record
                transaction = Transaction(
                    portfolio_id=portfolio_id,
                    symbol=symbol.upper(),
                    transaction_type=TransactionType.BUY,
                    quantity=quantity,
                    price=purchase_price,
                    total_amount=quantity * purchase_price,
                    status=TransactionStatus.COMPLETED
                )
                session.add(transaction)
                
                await session.commit()
                
                # Update current price
                await self._update_investment_price(investment)
                
                logger.info("Investment added", user_id=user_id, portfolio_id=portfolio_id, symbol=symbol, quantity=float(quantity))
                return investment
                
        except Exception as e:
            logger.error("Failed to add investment", portfolio_id=portfolio_id, symbol=symbol, error=str(e))
            return None
    
    async def get_portfolio_investments(self, portfolio_id: int, user_id: int) -> List[Investment]:
        """Get all investments in a portfolio."""
        try:
            # Verify portfolio ownership
            portfolio = await self.get_portfolio_by_id(portfolio_id, user_id)
            if not portfolio:
                return []
            
            async with get_db_session() as session:
                result = await session.execute(
                    select(Investment).where(
                        and_(
                            Investment.portfolio_id == portfolio_id,
                            Investment.deleted_at.is_(None)
                        )
                    ).order_by(Investment.created_at.desc())
                )
                
                investments = result.scalars().all()
                
                # Update current prices
                for investment in investments:
                    await self._update_investment_price(investment)
                
                return investments
                
        except Exception as e:
            logger.error("Failed to get portfolio investments", portfolio_id=portfolio_id, error=str(e))
            return []
    
    async def update_investment_quantity(
        self,
        investment_id: int,
        user_id: int,
        new_quantity: Decimal,
        transaction_type: TransactionType = TransactionType.BUY
    ) -> bool:
        """Update investment quantity (buy/sell)."""
        try:
            async with get_db_session() as session:
                # Get investment with portfolio ownership check
                result = await session.execute(
                    select(Investment).join(Portfolio).where(
                        and_(
                            Investment.id == investment_id,
                            Portfolio.user_id == user_id,
                            Investment.deleted_at.is_(None)
                        )
                    )
                )
                investment = result.scalar_one_or_none()
                
                if not investment:
                    logger.warning("Investment not found or access denied", investment_id=investment_id, user_id=user_id)
                    return False
                
                # Get current market price
                current_price = await self.market_service.get_asset_price(investment.symbol)
                price = Decimal(str(current_price.get('price', investment.current_price))) if current_price else investment.current_price
                
                if transaction_type == TransactionType.BUY:
                    # Calculate new average cost
                    total_value = (investment.quantity * investment.average_cost) + (new_quantity * price)
                    total_quantity = investment.quantity + new_quantity
                    investment.average_cost = total_value / total_quantity
                    investment.quantity = total_quantity
                    investment.total_invested += new_quantity * price
                    
                elif transaction_type == TransactionType.SELL:
                    if new_quantity > investment.quantity:
                        logger.warning("Cannot sell more than owned", investment_id=investment_id, owned=float(investment.quantity), selling=float(new_quantity))
                        return False
                    
                    investment.quantity -= new_quantity
                    # Reduce total invested proportionally
                    investment.total_invested *= (investment.quantity / (investment.quantity + new_quantity))
                
                # Create transaction record
                transaction = Transaction(
                    portfolio_id=investment.portfolio_id,
                    symbol=investment.symbol,
                    transaction_type=transaction_type,
                    quantity=new_quantity,
                    price=price,
                    total_amount=new_quantity * price,
                    status=TransactionStatus.COMPLETED
                )
                session.add(transaction)
                
                await session.commit()
                
                logger.info("Investment quantity updated", investment_id=investment_id, transaction_type=transaction_type.value, quantity=float(new_quantity))
                return True
                
        except Exception as e:
            logger.error("Failed to update investment quantity", investment_id=investment_id, error=str(e))
            return False
    
    async def get_portfolio_performance(self, portfolio_id: int, user_id: int) -> Dict[str, Any]:
        """Get portfolio performance metrics."""
        try:
            portfolio = await self.get_portfolio_by_id(portfolio_id, user_id)
            if not portfolio:
                return {}
            
            investments = await self.get_portfolio_investments(portfolio_id, user_id)
            
            total_invested = sum(inv.total_invested for inv in investments)
            total_current_value = sum(inv.current_value for inv in investments)
            total_pnl = total_current_value - total_invested
            pnl_percentage = (total_pnl / total_invested * 100) if total_invested > 0 else 0
            
            # Calculate individual investment performance
            investment_performance = []
            for inv in investments:
                inv_pnl = inv.current_value - inv.total_invested
                inv_pnl_pct = (inv_pnl / inv.total_invested * 100) if inv.total_invested > 0 else 0
                
                investment_performance.append({
                    "symbol": inv.symbol,
                    "quantity": float(inv.quantity),
                    "average_cost": float(inv.average_cost),
                    "current_price": float(inv.current_price),
                    "total_invested": float(inv.total_invested),
                    "current_value": float(inv.current_value),
                    "pnl": float(inv_pnl),
                    "pnl_percentage": float(inv_pnl_pct),
                    "weight": float((inv.current_value / total_current_value * 100) if total_current_value > 0 else 0)
                })
            
            return {
                "portfolio_id": portfolio_id,
                "portfolio_name": portfolio.name,
                "total_invested": float(total_invested),
                "current_value": float(total_current_value),
                "total_pnl": float(total_pnl),
                "pnl_percentage": float(pnl_percentage),
                "investment_count": len(investments),
                "investments": investment_performance,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Failed to get portfolio performance", portfolio_id=portfolio_id, error=str(e))
            return {}
    
    async def get_user_transactions(self, user_id: int, limit: int = 50) -> List[Transaction]:
        """Get user's transaction history."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(Transaction)
                    .join(Portfolio)
                    .where(Portfolio.user_id == user_id)
                    .order_by(Transaction.created_at.desc())
                    .limit(limit)
                )
                
                return result.scalars().all()
                
        except Exception as e:
            logger.error("Failed to get user transactions", user_id=user_id, error=str(e))
            return []
    
    async def _update_investment_price(self, investment: Investment) -> None:
        """Update investment current price from market data."""
        try:
            price_data = await self.market_service.get_asset_price(investment.symbol)
            if price_data and 'price' in price_data:
                investment.current_price = Decimal(str(price_data['price']))
                investment.last_price_update = datetime.utcnow()
                
                # Update current value
                investment.current_value = investment.quantity * investment.current_price
                
        except Exception as e:
            logger.error("Failed to update investment price", symbol=investment.symbol, error=str(e))
    
    async def _update_portfolio_value(self, portfolio: Portfolio) -> None:
        """Update portfolio total value."""
        try:
            async with get_db_session() as session:
                # Get all investments in portfolio
                result = await session.execute(
                    select(Investment).where(
                        and_(
                            Investment.portfolio_id == portfolio.id,
                            Investment.deleted_at.is_(None)
                        )
                    )
                )
                investments = result.scalars().all()
                
                total_invested = Decimal('0')
                total_value = Decimal('0')
                
                for investment in investments:
                    await self._update_investment_price(investment)
                    total_invested += investment.total_invested
                    total_value += investment.current_value
                
                portfolio.total_invested = total_invested
                portfolio.total_value = total_value
                portfolio.last_updated = datetime.utcnow()
                
                await session.commit()
                
        except Exception as e:
            logger.error("Failed to update portfolio value", portfolio_id=portfolio.id, error=str(e))
