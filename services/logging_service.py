"""
Logging service for comprehensive application logging and monitoring.
"""
import sys
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from database import get_db_session
from models.bot_data import SystemLog, UserInteraction, LogLevel, InteractionType

logger = structlog.get_logger(__name__)


class LoggingService:
    """Service for application logging and monitoring."""
    
    def __init__(self):
        self.logger = structlog.get_logger("LoggingService")
    
    async def log_user_interaction(
        self,
        user_id: int,
        interaction_type: str,
        action: str,
        context: Optional[str] = None,
        input_data: Optional[str] = None,
        output_data: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log user interaction to database."""
        try:
            async with get_db_session() as session:
                interaction = UserInteraction(
                    user_id=user_id,
                    interaction_type=InteractionType(interaction_type),
                    action=action,
                    context=context,
                    input_data=input_data,
                    output_data=output_data,
                    success=success,
                    error_message=error_message,
                    response_time_ms=response_time_ms,
                    session_id=session_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    metadata=metadata
                )
                
                session.add(interaction)
                await session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to log user interaction", error=str(e))
            return False
    
    async def log_system_event(
        self,
        level: str,
        logger_name: str,
        message: str,
        module: Optional[str] = None,
        function: Optional[str] = None,
        line_number: Optional[int] = None,
        user_id: Optional[int] = None,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        execution_time_ms: Optional[int] = None,
        memory_usage_mb: Optional[int] = None,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log system event to database."""
        try:
            async with get_db_session() as session:
                log_entry = SystemLog(
                    level=LogLevel(level.lower()),
                    logger_name=logger_name,
                    message=message,
                    module=module,
                    function=function,
                    line_number=line_number,
                    user_id=user_id,
                    request_id=request_id,
                    correlation_id=correlation_id,
                    execution_time_ms=execution_time_ms,
                    memory_usage_mb=memory_usage_mb,
                    extra_data=extra_data
                )
                
                session.add(log_entry)
                await session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to log system event", error=str(e))
            return False
    
    async def log_error(
        self,
        message: str,
        exception_info: Optional[tuple] = None,
        user_id: Optional[int] = None,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log error with exception information."""
        try:
            async with get_db_session() as session:
                log_entry = SystemLog.create_log(
                    level=LogLevel.ERROR,
                    logger_name="ErrorLogger",
                    message=message,
                    user_id=user_id,
                    request_id=request_id,
                    correlation_id=correlation_id,
                    exception_info=exception_info,
                    extra_data=extra_data
                )
                
                session.add(log_entry)
                await session.commit()
                
                # Also log to structured logger
                self.logger.error(
                    message,
                    user_id=user_id,
                    request_id=request_id,
                    correlation_id=correlation_id,
                    extra_data=extra_data
                )
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to log error", error=str(e))
            return False
    
    async def log_security_event(
        self,
        event_type: str,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log security-related events."""
        try:
            message = f"Security event: {event_type}"
            
            extra_data = {
                "event_type": event_type,
                "ip_address": ip_address,
                "user_agent": user_agent,
                **(details or {})
            }
            
            await self.log_system_event(
                level="warning",
                logger_name="SecurityLogger",
                message=message,
                user_id=user_id,
                extra_data=extra_data
            )
            
            # Also log to structured logger for immediate alerting
            self.logger.warning(
                message,
                user_id=user_id,
                ip_address=ip_address,
                event_type=event_type,
                details=details
            )
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to log security event", error=str(e))
            return False
    
    async def get_user_activity_logs(
        self,
        user_id: int,
        limit: int = 100,
        offset: int = 0
    ) -> list:
        """Get user activity logs."""
        try:
            async with get_db_session() as session:
                result = await session.execute(
                    select(UserInteraction)
                    .where(UserInteraction.user_id == user_id)
                    .order_by(UserInteraction.created_at.desc())
                    .limit(limit)
                    .offset(offset)
                )
                
                interactions = result.scalars().all()
                
                return [
                    {
                        "id": interaction.id,
                        "interaction_type": interaction.interaction_type.value,
                        "action": interaction.action,
                        "context": interaction.context,
                        "success": interaction.success,
                        "error_message": interaction.error_message,
                        "response_time_ms": interaction.response_time_ms,
                        "created_at": interaction.created_at.isoformat(),
                        "metadata": interaction.metadata
                    }
                    for interaction in interactions
                ]
                
        except Exception as e:
            self.logger.error("Failed to get user activity logs", user_id=user_id, error=str(e))
            return []
    
    async def get_system_logs(
        self,
        level: Optional[str] = None,
        logger_name: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> list:
        """Get system logs with optional filtering."""
        try:
            async with get_db_session() as session:
                query = select(SystemLog)
                
                if level:
                    query = query.where(SystemLog.level == LogLevel(level.lower()))
                
                if logger_name:
                    query = query.where(SystemLog.logger_name == logger_name)
                
                query = query.order_by(SystemLog.created_at.desc()).limit(limit).offset(offset)
                
                result = await session.execute(query)
                logs = result.scalars().all()
                
                return [
                    {
                        "id": log.id,
                        "level": log.level.value,
                        "logger_name": log.logger_name,
                        "message": log.message,
                        "module": log.module,
                        "function": log.function,
                        "line_number": log.line_number,
                        "exception_type": log.exception_type,
                        "exception_message": log.exception_message,
                        "user_id": log.user_id,
                        "request_id": log.request_id,
                        "correlation_id": log.correlation_id,
                        "execution_time_ms": log.execution_time_ms,
                        "memory_usage_mb": log.memory_usage_mb,
                        "created_at": log.created_at.isoformat(),
                        "extra_data": log.extra_data
                    }
                    for log in logs
                ]
                
        except Exception as e:
            self.logger.error("Failed to get system logs", error=str(e))
            return []
    
    async def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period."""
        try:
            from datetime import timedelta
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            async with get_db_session() as session:
                # Get error count
                result = await session.execute(
                    select(SystemLog)
                    .where(
                        SystemLog.level == LogLevel.ERROR,
                        SystemLog.created_at >= cutoff_time
                    )
                )
                error_logs = result.scalars().all()
                
                # Group by exception type
                error_types = {}
                for log in error_logs:
                    exc_type = log.exception_type or "Unknown"
                    if exc_type not in error_types:
                        error_types[exc_type] = 0
                    error_types[exc_type] += 1
                
                return {
                    "total_errors": len(error_logs),
                    "error_types": error_types,
                    "time_period_hours": hours,
                    "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
                }
                
        except Exception as e:
            self.logger.error("Failed to get error summary", error=str(e))
            return {"total_errors": 0, "error_types": {}, "time_period_hours": hours}
    
    async def cleanup_old_logs(self, days: int = 30) -> int:
        """Clean up old log entries."""
        try:
            from datetime import timedelta
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            
            async with get_db_session() as session:
                # Delete old user interactions
                result1 = await session.execute(
                    select(UserInteraction).where(UserInteraction.created_at < cutoff_time)
                )
                old_interactions = result1.scalars().all()
                
                for interaction in old_interactions:
                    await session.delete(interaction)
                
                # Delete old system logs (keep errors longer)
                result2 = await session.execute(
                    select(SystemLog).where(
                        SystemLog.created_at < cutoff_time,
                        SystemLog.level != LogLevel.ERROR
                    )
                )
                old_logs = result2.scalars().all()
                
                for log in old_logs:
                    await session.delete(log)
                
                await session.commit()
                
                total_deleted = len(old_interactions) + len(old_logs)
                self.logger.info("Old logs cleaned up", deleted_count=total_deleted, days=days)
                
                return total_deleted
                
        except Exception as e:
            self.logger.error("Failed to cleanup old logs", error=str(e))
            return 0
