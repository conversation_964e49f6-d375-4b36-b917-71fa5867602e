"""
Security service for encryption, authentication, and security utilities.
"""
import secrets
import hashlib
import hmac
import jwt
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import structlog

from config import config

logger = structlog.get_logger(__name__)


class SecurityService:
    """Service for security-related operations."""
    
    def __init__(self):
        self.secret_key = config.security.secret_key.encode()
        self.jwt_secret = config.security.jwt_secret_key
        self.encryption_key = self._derive_encryption_key(config.security.encryption_key)
        self.cipher_suite = Fernet(self.encryption_key)
    
    def _derive_encryption_key(self, password: str) -> bytes:
        """Derive encryption key from password."""
        password_bytes = password.encode()
        salt = b'salt_1234567890'  # In production, use a random salt per user
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def generate_session_token(self, length: int = 32) -> str:
        """Generate a secure session token."""
        return secrets.token_urlsafe(length)
    
    def generate_api_key(self, length: int = 32) -> str:
        """Generate a secure API key."""
        return secrets.token_urlsafe(length)
    
    def hash_password(self, password: str) -> str:
        """Hash a password using PBKDF2."""
        salt = secrets.token_bytes(32)
        pwdhash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000
        )
        return base64.b64encode(salt + pwdhash).decode('ascii')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        try:
            decoded = base64.b64decode(hashed_password.encode('ascii'))
            salt = decoded[:32]
            stored_hash = decoded[32:]
            
            pwdhash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt,
                100000
            )
            
            return hmac.compare_digest(stored_hash, pwdhash)
        except Exception as e:
            logger.error("Password verification failed", error=str(e))
            return False
    
    def create_jwt_token(
        self,
        user_id: int,
        telegram_id: int,
        role: str = "user",
        expires_hours: int = 24
    ) -> str:
        """Create a JWT token for user authentication."""
        try:
            payload = {
                'user_id': user_id,
                'telegram_id': telegram_id,
                'role': role,
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + timedelta(hours=expires_hours)
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
            return token
            
        except Exception as e:
            logger.error("JWT token creation failed", error=str(e))
            return ""
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode a JWT token."""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning("Invalid JWT token", error=str(e))
            return None
        except Exception as e:
            logger.error("JWT token verification failed", error=str(e))
            return None
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error("Data encryption failed", error=str(e))
            return ""
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error("Data decryption failed", error=str(e))
            return ""
    
    def generate_secure_hash(self, data: str) -> str:
        """Generate a secure hash of data."""
        return hashlib.sha256(data.encode()).hexdigest()
    
    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature."""
        try:
            expected_signature = hmac.new(
                self.secret_key,
                payload.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error("Webhook signature verification failed", error=str(e))
            return False
    
    def sanitize_input(self, input_text: str, max_length: int = 1000) -> str:
        """Sanitize user input to prevent injection attacks."""
        if not input_text:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\n', '\r', '\t']
        sanitized = input_text
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        # Limit length
        sanitized = sanitized[:max_length]
        
        # Remove leading/trailing whitespace
        sanitized = sanitized.strip()
        
        return sanitized
    
    def validate_telegram_data(self, data: Dict[str, Any]) -> bool:
        """Validate data received from Telegram."""
        try:
            # Check required fields
            required_fields = ['id', 'first_name']
            for field in required_fields:
                if field not in data:
                    return False
            
            # Validate ID is numeric
            if not isinstance(data['id'], int) or data['id'] <= 0:
                return False
            
            # Validate name length
            if len(data['first_name']) > 255 or len(data['first_name']) == 0:
                return False
            
            # Validate optional fields
            if 'username' in data and len(data['username']) > 255:
                return False
            
            if 'last_name' in data and len(data['last_name']) > 255:
                return False
            
            return True
            
        except Exception as e:
            logger.error("Telegram data validation failed", error=str(e))
            return False
    
    def is_safe_url(self, url: str) -> bool:
        """Check if URL is safe (basic validation)."""
        try:
            if not url:
                return False
            
            # Check for dangerous protocols
            dangerous_protocols = ['javascript:', 'data:', 'vbscript:', 'file:']
            url_lower = url.lower()
            
            for protocol in dangerous_protocols:
                if url_lower.startswith(protocol):
                    return False
            
            # Must start with http or https
            if not (url_lower.startswith('http://') or url_lower.startswith('https://')):
                return False
            
            return True
            
        except Exception as e:
            logger.error("URL safety check failed", error=str(e))
            return False
    
    def generate_csrf_token(self) -> str:
        """Generate CSRF token."""
        return secrets.token_urlsafe(32)
    
    def verify_csrf_token(self, token: str, expected_token: str) -> bool:
        """Verify CSRF token."""
        return hmac.compare_digest(token, expected_token)
    
    def rate_limit_key(self, user_id: int, action: str) -> str:
        """Generate rate limit key for user action."""
        return f"rate_limit:{user_id}:{action}"
    
    def audit_log_entry(
        self,
        user_id: int,
        action: str,
        resource: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create audit log entry."""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'success': success,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'session_id': self.generate_session_token(16)
        }
