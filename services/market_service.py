"""
Market service for fetching real-time market data and prices.
"""
import aiohttp
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import structlog

from config import config

logger = structlog.get_logger(__name__)


class MarketService:
    """Service for fetching market data from various APIs."""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    def _is_cache_valid(self, symbol: str) -> bool:
        """Check if cached data is still valid."""
        if symbol not in self.cache:
            return False
        
        cache_time = self.cache[symbol].get('cached_at')
        if not cache_time:
            return False
        
        return (datetime.utcnow() - cache_time).total_seconds() < self.cache_ttl
    
    def _cache_data(self, symbol: str, data: Dict[str, Any]) -> None:
        """Cache market data."""
        data['cached_at'] = datetime.utcnow()
        self.cache[symbol] = data
    
    async def get_asset_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current price for an asset."""
        try:
            symbol = symbol.upper()
            
            # Check cache first
            if self._is_cache_valid(symbol):
                cached_data = self.cache[symbol].copy()
                cached_data.pop('cached_at', None)
                return cached_data
            
            # Try different data sources
            price_data = None
            
            # Try CoinMarketCap for crypto
            if self._is_crypto_symbol(symbol):
                price_data = await self._get_crypto_price_cmc(symbol)
            
            # Try Alpha Vantage for stocks
            if not price_data:
                price_data = await self._get_stock_price_alpha_vantage(symbol)
            
            # Fallback to mock data for development
            if not price_data and config.environment == "development":
                price_data = self._get_mock_price_data(symbol)
            
            if price_data:
                self._cache_data(symbol, price_data)
                return price_data
            
            logger.warning("No price data found", symbol=symbol)
            return None
            
        except Exception as e:
            logger.error("Failed to get asset price", symbol=symbol, error=str(e))
            return None
    
    async def get_top_cryptocurrencies(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top cryptocurrencies by market cap."""
        try:
            if config.api.coinmarketcap_api_key:
                return await self._get_top_crypto_cmc(limit)
            else:
                # Return mock data for development
                return self._get_mock_top_crypto(limit)
                
        except Exception as e:
            logger.error("Failed to get top cryptocurrencies", error=str(e))
            return []
    
    async def get_market_trends(self) -> Dict[str, Any]:
        """Get market trends and statistics."""
        try:
            # This would integrate with various market data APIs
            # For now, return basic structure
            return {
                "market_cap_change_24h": 2.5,
                "volume_change_24h": -5.2,
                "bitcoin_dominance": 42.3,
                "fear_greed_index": 65,
                "trending_coins": await self.get_top_cryptocurrencies(5)
            }
            
        except Exception as e:
            logger.error("Failed to get market trends", error=str(e))
            return {}
    
    async def _get_crypto_price_cmc(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cryptocurrency price from CoinMarketCap."""
        try:
            if not config.api.coinmarketcap_api_key:
                return None
            
            session = await self._get_session()
            url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"
            
            headers = {
                'X-CMC_PRO_API_KEY': config.api.coinmarketcap_api_key,
                'Accept': 'application/json'
            }
            
            params = {
                'symbol': symbol,
                'convert': 'USD'
            }
            
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'data' in data and symbol in data['data']:
                        coin_data = data['data'][symbol]
                        quote = coin_data['quote']['USD']
                        
                        return {
                            'symbol': symbol,
                            'name': coin_data['name'],
                            'price': quote['price'],
                            'price_change_24h': quote['percent_change_24h'],
                            'volume_24h': quote['volume_24h'],
                            'market_cap': quote['market_cap'],
                            'last_updated': quote['last_updated'],
                            'source': 'coinmarketcap'
                        }
                
                logger.warning("CoinMarketCap API error", status=response.status, symbol=symbol)
                return None
                
        except Exception as e:
            logger.error("CoinMarketCap API request failed", symbol=symbol, error=str(e))
            return None
    
    async def _get_stock_price_alpha_vantage(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get stock price from Alpha Vantage."""
        try:
            if not config.api.alpha_vantage_api_key:
                return None
            
            session = await self._get_session()
            url = "https://www.alphavantage.co/query"
            
            params = {
                'function': 'GLOBAL_QUOTE',
                'symbol': symbol,
                'apikey': config.api.alpha_vantage_api_key
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'Global Quote' in data:
                        quote = data['Global Quote']
                        
                        return {
                            'symbol': symbol,
                            'name': symbol,  # Alpha Vantage doesn't provide company name in this endpoint
                            'price': float(quote['05. price']),
                            'price_change_24h': float(quote['10. change percent'].rstrip('%')),
                            'volume_24h': float(quote['06. volume']),
                            'market_cap': None,  # Not available in this endpoint
                            'last_updated': datetime.utcnow().isoformat(),
                            'source': 'alpha_vantage'
                        }
                
                logger.warning("Alpha Vantage API error", status=response.status, symbol=symbol)
                return None
                
        except Exception as e:
            logger.error("Alpha Vantage API request failed", symbol=symbol, error=str(e))
            return None
    
    async def _get_top_crypto_cmc(self, limit: int) -> List[Dict[str, Any]]:
        """Get top cryptocurrencies from CoinMarketCap."""
        try:
            if not config.api.coinmarketcap_api_key:
                return []
            
            session = await self._get_session()
            url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest"
            
            headers = {
                'X-CMC_PRO_API_KEY': config.api.coinmarketcap_api_key,
                'Accept': 'application/json'
            }
            
            params = {
                'start': 1,
                'limit': limit,
                'convert': 'USD'
            }
            
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'data' in data:
                        result = []
                        for coin in data['data']:
                            quote = coin['quote']['USD']
                            result.append({
                                'symbol': coin['symbol'],
                                'name': coin['name'],
                                'price': quote['price'],
                                'price_change_24h': quote['percent_change_24h'],
                                'volume_24h': quote['volume_24h'],
                                'market_cap': quote['market_cap'],
                                'market_cap_rank': coin['cmc_rank']
                            })
                        return result
                
                logger.warning("CoinMarketCap listings API error", status=response.status)
                return []
                
        except Exception as e:
            logger.error("CoinMarketCap listings API request failed", error=str(e))
            return []
    
    def _is_crypto_symbol(self, symbol: str) -> bool:
        """Check if symbol is likely a cryptocurrency."""
        crypto_symbols = {
            'BTC', 'ETH', 'TRX', 'USDT', 'BNB', 'ADA', 'XRP', 'SOL', 'DOT', 'DOGE',
            'MATIC', 'SHIB', 'AVAX', 'UNI', 'LINK', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP'
        }
        return symbol.upper() in crypto_symbols
    
    def _get_mock_price_data(self, symbol: str) -> Dict[str, Any]:
        """Generate mock price data for development."""
        import random
        
        # Base prices for common symbols
        base_prices = {
            'BTC': 45000,
            'ETH': 3000,
            'TRX': 0.08,
            'USDT': 1.0,
            'BNB': 300,
            'AAPL': 150,
            'GOOGL': 2500,
            'TSLA': 800,
            'MSFT': 300,
            'AMZN': 3200
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # Add some random variation
        price = base_price * (1 + random.uniform(-0.1, 0.1))
        change_24h = random.uniform(-10, 10)
        volume = random.uniform(1000000, 100000000)
        
        return {
            'symbol': symbol,
            'name': f"{symbol} Mock Data",
            'price': round(price, 2),
            'price_change_24h': round(change_24h, 2),
            'volume_24h': round(volume, 0),
            'market_cap': round(price * 1000000, 0),
            'last_updated': datetime.utcnow().isoformat(),
            'source': 'mock'
        }
    
    def _get_mock_top_crypto(self, limit: int) -> List[Dict[str, Any]]:
        """Generate mock top cryptocurrency data."""
        symbols = ['BTC', 'ETH', 'TRX', 'USDT', 'BNB', 'ADA', 'XRP', 'SOL', 'DOT', 'DOGE']
        
        result = []
        for i, symbol in enumerate(symbols[:limit]):
            mock_data = self._get_mock_price_data(symbol)
            mock_data['market_cap_rank'] = i + 1
            result.append(mock_data)
        
        return result
