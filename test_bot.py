#!/usr/bin/env python3
"""
Simple test script to verify bot functionality.
"""
import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_imports():
    """Test that all modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from config import config
        print("✅ Config imported successfully")
        
        from database import DatabaseManager
        print("✅ Database manager imported successfully")
        
        from models.user import User, UserProfile
        print("✅ User models imported successfully")
        
        from models.investment import Portfolio, Investment, Transaction
        print("✅ Investment models imported successfully")
        
        from services.user_service import UserService
        print("✅ User service imported successfully")
        
        from services.investment_service import InvestmentService
        print("✅ Investment service imported successfully")
        
        from services.market_service import MarketService
        print("✅ Market service imported successfully")
        
        from bot.bot import InvestmentBot
        print("✅ Bot imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_config():
    """Test configuration loading."""
    print("\n🧪 Testing configuration...")
    
    try:
        from config import config
        
        # Test basic config access
        print(f"✅ Environment: {config.environment}")
        print(f"✅ Debug mode: {config.debug}")
        
        # Test validation
        config.validate()
        print("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

async def test_services():
    """Test service initialization."""
    print("\n🧪 Testing services...")
    
    try:
        from services.user_service import UserService
        from services.investment_service import InvestmentService
        from services.market_service import MarketService
        
        user_service = UserService()
        print("✅ User service initialized")
        
        investment_service = InvestmentService()
        print("✅ Investment service initialized")
        
        market_service = MarketService()
        print("✅ Market service initialized")
        
        # Test market service mock data
        mock_data = market_service._get_mock_price_data("BTC")
        if mock_data and 'price' in mock_data:
            print(f"✅ Mock market data: BTC = ${mock_data['price']}")
        
        await market_service.close()
        print("✅ Market service closed properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Service error: {e}")
        return False

async def test_database_connection():
    """Test database connection (if configured)."""
    print("\n🧪 Testing database connection...")
    
    try:
        from database import DatabaseManager
        from config import config
        
        if not config.database.url:
            print("⚠️  No database URL configured, skipping database test")
            return True
        
        db_manager = DatabaseManager()
        
        # Test connection
        is_healthy = await db_manager.health_check()
        if is_healthy:
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        await db_manager.close()
        print("✅ Database connection closed properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        print("💡 Make sure your database is running and .env is configured")
        return False

async def test_bot_initialization():
    """Test bot initialization."""
    print("\n🧪 Testing bot initialization...")
    
    try:
        from bot.bot import InvestmentBot
        from config import config
        
        if not config.telegram.bot_token or config.telegram.bot_token == "your_bot_token_here":
            print("⚠️  No bot token configured, skipping bot initialization test")
            return True
        
        bot = InvestmentBot()
        print("✅ Bot instance created")
        
        # Note: We don't actually initialize the bot here as it would try to connect to Telegram
        print("✅ Bot initialization test passed (token configured)")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization error: {e}")
        return False

async def run_all_tests():
    """Run all tests."""
    print("🚀 Starting Advanced Investment Bot Tests")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Configuration Tests", test_config),
        ("Service Tests", test_services),
        ("Database Tests", test_database_connection),
        ("Bot Initialization Tests", test_bot_initialization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your bot setup looks good.")
        print("\n📋 Next steps:")
        print("1. Configure your .env file with real API keys")
        print("2. Set up your database (PostgreSQL recommended)")
        print("3. Run: python main.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n💡 Common issues:")
        print("- Missing dependencies: pip install -r requirements.txt")
        print("- Database not running or misconfigured")
        print("- Missing .env file: cp .env.example .env")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        sys.exit(1)
