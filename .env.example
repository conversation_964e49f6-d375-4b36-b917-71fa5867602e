# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/investment_bot
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# API Keys
COINMARKETCAP_API_KEY=your_cmc_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/bot.log

# Application Settings
ENVIRONMENT=development
DEBUG=True
MAX_USERS_PER_HOUR=100
RATE_LIMIT_REQUESTS=30
RATE_LIMIT_WINDOW=60

# Investment Settings
MIN_INVESTMENT_AMOUNT=10.0
MAX_INVESTMENT_AMOUNT=10000.0
DEFAULT_CURRENCY=USD
SUPPORTED_CRYPTOCURRENCIES=BTC,ETH,TRX,USDT,BNB

# Admin Settings
ADMIN_USER_IDS=123456789,987654321
SUPER_ADMIN_ID=123456789

# Backup and Maintenance
BACKUP_INTERVAL_HOURS=24
MAINTENANCE_MODE=False
MAINTENANCE_MESSAGE=Bot is under maintenance. Please try again later.
