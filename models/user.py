"""
User-related database models.
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum
from sqlalchemy import (
    BigInteger, String, Boolean, DateTime, Text, 
    Numeric, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin, SoftDeleteMixin


class UserRole(Enum):
    """User role enumeration."""
    USER = "user"
    PREMIUM = "premium"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class UserStatus(Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"


class User(Base, TimestampMixin, SoftDeleteMixin):
    """Main user model for Telegram users."""
    
    __tablename__ = "users"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Telegram user information
    telegram_id: Mapped[int] = mapped_column(BigInteger, unique=True, nullable=False, index=True)
    username: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    first_name: Mapped[str] = mapped_column(String(255), nullable=False)
    last_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    language_code: Mapped[Optional[str]] = mapped_column(String(10), nullable=True, default="en")
    
    # User status and role
    status: Mapped[UserStatus] = mapped_column(String(20), nullable=False, default=UserStatus.ACTIVE)
    role: Mapped[UserRole] = mapped_column(String(20), nullable=False, default=UserRole.USER)
    
    # Security and verification
    is_verified: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    is_premium: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    phone_number: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    
    # Activity tracking
    last_activity: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    registration_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        default=datetime.utcnow
    )
    
    # Referral system
    referral_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, unique=True, index=True)
    referred_by_id: Mapped[Optional[int]] = mapped_column(
        BigInteger, 
        ForeignKey("users.id"), 
        nullable=True
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_user_telegram_id", "telegram_id"),
        Index("idx_user_username", "username"),
        Index("idx_user_status_role", "status", "role"),
        Index("idx_user_referral_code", "referral_code"),
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, telegram_id={self.telegram_id}, username={self.username})>"
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name
    
    @property
    def display_name(self) -> str:
        """Get user's display name (username or full name)."""
        return self.username or self.full_name
    
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]
    
    def can_access_admin_features(self) -> bool:
        """Check if user can access admin features."""
        return self.is_admin() and self.status == UserStatus.ACTIVE
    
    def update_last_activity(self) -> None:
        """Update user's last activity timestamp."""
        self.last_activity = datetime.utcnow()


class UserProfile(Base, TimestampMixin):
    """Extended user profile information."""
    
    __tablename__ = "user_profiles"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign key to user
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False,
        unique=True
    )
    
    # Profile information
    bio: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    timezone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, default="UTC")
    
    # Investment preferences
    risk_tolerance: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # low, medium, high
    investment_experience: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # beginner, intermediate, expert
    preferred_currency: Mapped[str] = mapped_column(String(10), nullable=False, default="USD")
    
    # Notification preferences
    notifications_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    price_alerts_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    portfolio_updates_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    
    # Privacy settings
    profile_public: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    show_portfolio: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    
    def __repr__(self) -> str:
        return f"<UserProfile(id={self.id}, user_id={self.user_id})>"


class UserSession(Base, TimestampMixin):
    """User session tracking for security and analytics."""
    
    __tablename__ = "user_sessions"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign key to user
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Session information
    session_token: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)  # IPv6 support
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Session status
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_used: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        default=datetime.utcnow
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_session_token", "session_token"),
        Index("idx_session_user_active", "user_id", "is_active"),
        Index("idx_session_expires", "expires_at"),
    )
    
    def __repr__(self) -> str:
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
    
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def invalidate(self) -> None:
        """Invalidate the session."""
        self.is_active = False
    
    def refresh(self, extend_hours: int = 24) -> None:
        """Refresh session expiration."""
        from datetime import timedelta
        self.expires_at = datetime.utcnow() + timedelta(hours=extend_hours)
        self.last_used = datetime.utcnow()
