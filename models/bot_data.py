"""
Bot-specific database models for messages, interactions, and system logs.
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum
from sqlalchemy import (
    BigInteger, String, Boolean, DateTime, Text, 
    Integer, ForeignKey, Index, JSON
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class MessageType(Enum):
    """Message type enumeration."""
    TEXT = "text"
    PHOTO = "photo"
    VIDEO = "video"
    DOCUMENT = "document"
    AUDIO = "audio"
    VOICE = "voice"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"
    POLL = "poll"
    CALLBACK_QUERY = "callback_query"
    INLINE_QUERY = "inline_query"


class InteractionType(Enum):
    """User interaction type enumeration."""
    COMMAND = "command"
    BUTTON_CLICK = "button_click"
    INLINE_QUERY = "inline_query"
    MESSAGE = "message"
    CALLBACK_QUERY = "callback_query"
    ERROR = "error"


class LogLevel(Enum):
    """System log level enumeration."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class BotMessage(Base, TimestampMixin):
    """Bot messages sent to users."""
    
    __tablename__ = "bot_messages"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign key to user
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Message details
    telegram_message_id: Mapped[int] = mapped_column(BigInteger, nullable=False, index=True)
    chat_id: Mapped[int] = mapped_column(BigInteger, nullable=False, index=True)
    message_type: Mapped[MessageType] = mapped_column(String(20), nullable=False)
    
    # Message content
    text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    caption: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    file_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    file_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Message metadata
    reply_to_message_id: Mapped[Optional[int]] = mapped_column(BigInteger, nullable=True)
    forward_from_user_id: Mapped[Optional[int]] = mapped_column(BigInteger, nullable=True)
    edit_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Bot response information
    is_bot_message: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    command_triggered: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    response_time_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Message processing
    is_processed: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    processing_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Additional data
    raw_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_bot_message_user_id", "user_id"),
        Index("idx_bot_message_chat_id", "chat_id"),
        Index("idx_bot_message_telegram_id", "telegram_message_id"),
        Index("idx_bot_message_type", "message_type"),
        Index("idx_bot_message_command", "command_triggered"),
        Index("idx_bot_message_created", "created_at"),
    )
    
    def __repr__(self) -> str:
        return f"<BotMessage(id={self.id}, user_id={self.user_id}, type={self.message_type})>"


class UserInteraction(Base, TimestampMixin):
    """User interactions with the bot for analytics."""
    
    __tablename__ = "user_interactions"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign key to user
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Interaction details
    interaction_type: Mapped[InteractionType] = mapped_column(String(20), nullable=False)
    action: Mapped[str] = mapped_column(String(100), nullable=False)
    context: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Interaction data
    input_data: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    output_data: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    success: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Performance metrics
    response_time_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Session information
    session_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Additional metadata
    metadata: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_interaction_user_id", "user_id"),
        Index("idx_interaction_type", "interaction_type"),
        Index("idx_interaction_action", "action"),
        Index("idx_interaction_success", "success"),
        Index("idx_interaction_created", "created_at"),
        Index("idx_interaction_session", "session_id"),
    )
    
    def __repr__(self) -> str:
        return f"<UserInteraction(id={self.id}, user_id={self.user_id}, action={self.action})>"


class SystemLog(Base, TimestampMixin):
    """System logs for monitoring and debugging."""
    
    __tablename__ = "system_logs"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Log details
    level: Mapped[LogLevel] = mapped_column(String(20), nullable=False, index=True)
    logger_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Context information
    module: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    function: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    line_number: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Error information
    exception_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    exception_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    stack_trace: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Request context
    user_id: Mapped[Optional[int]] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="SET NULL"), 
        nullable=True
    )
    request_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    correlation_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    
    # Performance metrics
    execution_time_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    memory_usage_mb: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Additional data
    extra_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_system_log_level", "level"),
        Index("idx_system_log_logger", "logger_name"),
        Index("idx_system_log_created", "created_at"),
        Index("idx_system_log_user_id", "user_id"),
        Index("idx_system_log_request_id", "request_id"),
        Index("idx_system_log_correlation_id", "correlation_id"),
        Index("idx_system_log_level_created", "level", "created_at"),
    )
    
    def __repr__(self) -> str:
        return f"<SystemLog(id={self.id}, level={self.level}, logger={self.logger_name})>"
    
    @classmethod
    def create_log(
        cls,
        level: LogLevel,
        logger_name: str,
        message: str,
        user_id: Optional[int] = None,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        exception_info: Optional[tuple] = None,
        extra_data: Optional[dict] = None
    ) -> "SystemLog":
        """Create a new system log entry."""
        log_entry = cls(
            level=level,
            logger_name=logger_name,
            message=message,
            user_id=user_id,
            request_id=request_id,
            correlation_id=correlation_id,
            extra_data=extra_data
        )
        
        if exception_info:
            exc_type, exc_value, exc_traceback = exception_info
            if exc_type:
                log_entry.exception_type = exc_type.__name__
            if exc_value:
                log_entry.exception_message = str(exc_value)
            if exc_traceback:
                import traceback
                log_entry.stack_trace = ''.join(traceback.format_tb(exc_traceback))
        
        return log_entry
