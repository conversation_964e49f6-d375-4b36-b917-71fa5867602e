"""
Investment-related database models.
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum
from decimal import Decimal
from sqlalchemy import (
    BigInteger, String, Boolean, DateTime, Text, 
    Numeric, ForeignKey, Index, UniqueConstraint, JSON
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin, SoftDeleteMixin


class InvestmentType(Enum):
    """Investment type enumeration."""
    CRYPTOCURRENCY = "cryptocurrency"
    STOCK = "stock"
    FOREX = "forex"
    COMMODITY = "commodity"
    INDEX = "index"
    BOND = "bond"


class TransactionType(Enum):
    """Transaction type enumeration."""
    BUY = "buy"
    SELL = "sell"
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    DIVIDEND = "dividend"
    FEE = "fee"
    TRANSFER = "transfer"


class TransactionStatus(Enum):
    """Transaction status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Portfolio(Base, TimestampMixin, SoftDeleteMixin):
    """User investment portfolio."""
    
    __tablename__ = "portfolios"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign key to user
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Portfolio information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    currency: Mapped[str] = mapped_column(String(10), nullable=False, default="USD")
    
    # Portfolio metrics
    total_value: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    total_invested: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    total_profit_loss: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    profit_loss_percentage: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=4), 
        nullable=False, 
        default=Decimal('0')
    )
    
    # Portfolio settings
    is_default: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    is_public: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    auto_rebalance: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    
    # Risk management
    risk_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # low, medium, high
    max_loss_percentage: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=5, scale=2), 
        nullable=True
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_portfolio_user_id", "user_id"),
        Index("idx_portfolio_user_default", "user_id", "is_default"),
        UniqueConstraint("user_id", "name", name="uq_portfolio_user_name"),
    )
    
    def __repr__(self) -> str:
        return f"<Portfolio(id={self.id}, user_id={self.user_id}, name={self.name})>"
    
    @property
    def profit_loss_percentage_float(self) -> float:
        """Get profit/loss percentage as float."""
        return float(self.profit_loss_percentage)
    
    def calculate_metrics(self) -> None:
        """Calculate portfolio metrics based on investments."""
        # This would be implemented to calculate total value, profit/loss, etc.
        pass


class Investment(Base, TimestampMixin, SoftDeleteMixin):
    """Individual investment holdings."""
    
    __tablename__ = "investments"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign keys
    portfolio_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("portfolios.id", ondelete="CASCADE"), 
        nullable=False
    )
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Investment details
    symbol: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    investment_type: Mapped[InvestmentType] = mapped_column(String(20), nullable=False)
    
    # Holdings
    quantity: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    average_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    current_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    
    # Investment metrics
    total_invested: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    current_value: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    profit_loss: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    profit_loss_percentage: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=4), 
        nullable=False, 
        default=Decimal('0')
    )
    
    # Price tracking
    last_price_update: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    price_change_24h: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=10, scale=4), 
        nullable=True
    )
    
    # Additional data
    metadata: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_investment_portfolio_id", "portfolio_id"),
        Index("idx_investment_user_id", "user_id"),
        Index("idx_investment_symbol", "symbol"),
        Index("idx_investment_type", "investment_type"),
        UniqueConstraint("portfolio_id", "symbol", name="uq_investment_portfolio_symbol"),
    )
    
    def __repr__(self) -> str:
        return f"<Investment(id={self.id}, symbol={self.symbol}, quantity={self.quantity})>"
    
    def update_current_price(self, new_price: Decimal) -> None:
        """Update current price and recalculate metrics."""
        self.current_price = new_price
        self.current_value = self.quantity * new_price
        self.profit_loss = self.current_value - self.total_invested
        
        if self.total_invested > 0:
            self.profit_loss_percentage = (self.profit_loss / self.total_invested) * 100
        else:
            self.profit_loss_percentage = Decimal('0')
        
        self.last_price_update = datetime.utcnow()


class Transaction(Base, TimestampMixin):
    """Investment transactions."""
    
    __tablename__ = "transactions"
    
    # Primary key
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    
    # Foreign keys
    user_id: Mapped[int] = mapped_column(
        BigInteger, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    portfolio_id: Mapped[Optional[int]] = mapped_column(
        BigInteger, 
        ForeignKey("portfolios.id", ondelete="SET NULL"), 
        nullable=True
    )
    investment_id: Mapped[Optional[int]] = mapped_column(
        BigInteger, 
        ForeignKey("investments.id", ondelete="SET NULL"), 
        nullable=True
    )
    
    # Transaction details
    transaction_type: Mapped[TransactionType] = mapped_column(String(20), nullable=False)
    status: Mapped[TransactionStatus] = mapped_column(String(20), nullable=False, default=TransactionStatus.PENDING)
    
    # Asset information
    symbol: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, index=True)
    asset_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Transaction amounts
    quantity: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    price_per_unit: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    total_amount: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    fee: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    net_amount: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=8), 
        nullable=False, 
        default=Decimal('0')
    )
    
    # Transaction metadata
    currency: Mapped[str] = mapped_column(String(10), nullable=False, default="USD")
    exchange: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    external_transaction_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Processing information
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Additional data
    metadata: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_transaction_user_id", "user_id"),
        Index("idx_transaction_portfolio_id", "portfolio_id"),
        Index("idx_transaction_type_status", "transaction_type", "status"),
        Index("idx_transaction_symbol", "symbol"),
        Index("idx_transaction_created", "created_at"),
        Index("idx_transaction_external_id", "external_transaction_id"),
    )
    
    def __repr__(self) -> str:
        return f"<Transaction(id={self.id}, type={self.transaction_type}, symbol={self.symbol}, amount={self.total_amount})>"
    
    def mark_completed(self) -> None:
        """Mark transaction as completed."""
        self.status = TransactionStatus.COMPLETED
        self.processed_at = datetime.utcnow()
    
    def mark_failed(self, error_message: str) -> None:
        """Mark transaction as failed with error message."""
        self.status = TransactionStatus.FAILED
        self.error_message = error_message
        self.processed_at = datetime.utcnow()
